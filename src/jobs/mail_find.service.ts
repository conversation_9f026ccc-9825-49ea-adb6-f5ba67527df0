import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { <PERSON>ron } from "@nestjs/schedule";
import { spawn } from "child_process";
import { MailFindService } from "src/modules/giant/services/mail_find.service";

@Injectable()
export class MailFindRemoveService {
  constructor(
    private readonly mailFindService: MailFindService
  ) {
  }

  @Cron('0 1 * * *')
  removeFind() {
    console.log('schedule mail find remove ...')
    this.mailFindService.removeInvalidFind()
  }
}