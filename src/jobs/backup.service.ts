import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { <PERSON>ron } from "@nestjs/schedule";
import { spawn } from "child_process";

@Injectable()
export class BackupService {
  private readonly backupPath: string
  constructor(
    private readonly configService: ConfigService
  ) {
    this.backupPath = this.configService.get<string>(
      'backup.path'
    )
  }

  // @Cron('0 1 * * *')
  // TODO in docker mongodump not available
  backup() {
    console.log('schedule backup ...')
    const handle = spawn('mongodump', [
      '-h', '127.0.0.1', '-d', 'kraken', '-o', this.backupPath
    ])

    // save backup time
    handle.on('close', async () => {

    })
  }
}