import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { <PERSON>ron } from "@nestjs/schedule";
import { spawn } from "child_process";
import { MailBindService } from "src/modules/giant/services/mail_bind.service";

@Injectable()
export class MailBindRemoveService {
  constructor(
    private readonly mailBindService: MailBindService
  ) {
  }

  @Cron('0 1 * * *')
  removeBind() {
    console.log('schedule mail bind remove ...')
    this.mailBindService.removeInvalidBind()
  }
}