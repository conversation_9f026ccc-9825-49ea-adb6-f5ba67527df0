import { DynamicModule, Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { GiantModule } from 'src/modules/giant/giant.module';
import { BackupService } from './backup.service';
import { MailBindRemoveService } from './mail_bind.service';
import { MailFindRemoveService } from './mail_find.service';

@Module({})
export class JobsModule {
    static register(): DynamicModule {
        if (process.env.APP_JOB_ON === 'true') {
            return {
                module: JobsModule,
                controllers: [],
                providers: [
                    // BackupService,
                    MailBindRemoveService,
                    MailFindRemoveService,
                ],
                imports: [ScheduleModule.forRoot(), GiantModule],
                exports: [],
            };
        }

        return {
            module: JobsModule,
            providers: [],
            exports: [],
            controllers: [],
            imports: [],
        };
    }
}
