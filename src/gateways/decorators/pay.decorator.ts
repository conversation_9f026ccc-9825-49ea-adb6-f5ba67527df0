import { applyDecorators, createParamDecorator, ExecutionContext, UseGuards } from '@nestjs/common';
import { GiantGuard, GiantMachineExceedGuard } from 'src/common/auth/guards/giant/auth.giant.guard';
import { PaySessionGuard } from '../guards/pay-admin.guard';
import { PayGuard } from '../guards/pay.guard';

// export const GetGiant = createParamDecorator(
//     (data: string, ctx: ExecutionContext): IGiantDocument => {
//         const { __giant } = ctx.switchToHttp().getRequest();
//         return data ? __giant[data] : __giant;
//     }
// );

export function AuthPay(): any {
    return applyDecorators(
        UseGuards(PaySessionGuard)
    );
}

export function CanPay(): any {
    return applyDecorators(
        UseGuards(PayGuard)
    );
}
