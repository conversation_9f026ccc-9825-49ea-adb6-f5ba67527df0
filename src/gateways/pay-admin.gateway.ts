import { Injectable } from "@nestjs/common";
import { MessageBody, SubscribeMessage, WebSocketGateway, OnGatewayDisconnect, WebSocketServer } from "@nestjs/websockets";
import { v4 } from "uuid";
import { AuthPay } from "./decorators/pay.decorator";
import { PayGateway } from "./pay.gateway";
import { PayAdminService } from "./services/pay-admin.service";

@WebSocketGateway({
  path: '/ws/1sssdf4-ads-adfsa-23fsadf-EerJ-Jlkadsjf/receivePay',
})
export class PayAdminGateway implements OnGatewayDisconnect {
  @WebSocketServer() private server: any;

  private disconnectInterval: NodeJS.Timeout | null = null;

  constructor(
    private payGateway: PayGateway,
    private payAdminService: PayAdminService
  ) {
  }

  sendToClient() {
    console.log('sending ....')
    console.log(this.payAdminService.getClient())
  }

  @AuthPay()
  @SubscribeMessage('handshake')
  handShake(@MessageBody() data: string) {
    console.log('handshake', data)
    this.clearDisconnectInterval()
    return this.payAdminService.getClient()
  }

  @AuthPay()
  @SubscribeMessage('heartbeat')
  heartbeat(@MessageBody() data: string) {
    console.log('heartbeat')
    return ''
  }

  @AuthPay()
  @SubscribeMessage('wechat')
  async wechat(@MessageBody() data: string) {
    const price = this.payAdminService.getPrice(data)
    const result = await this.payGateway.payFor(price)
    return {
      event: result ? 'key' : 'unknown',
      data: result
    }
  }

  handleDisconnect() {
    this.payAdminService.releaseClient()

    console.log('notifying dingtalk')
    this.notifyToDingTalk()
    this.disconnectInterval = setInterval(() => {
      console.log('notifying dingtalk timeout')
      this.notifyToDingTalk()
    }, 60000) // Notify every 5 minutes

    console.log('disc...')
  }

  private async notifyToDingTalk() {
    try {
      const response = await fetch('https://oapi.dingtalk.com/robot/send?access_token=b5d7a9fa2df79363f9a3612870fb7f1796ab4be3882f89920e87dcc9af69c889', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          "msgtype": "text",
          "text": {
            "content": "New topic: service down"
          }
        })
      });
      
      if (response.ok) {
        console.log('DingTalk notification sent successfully');
      } else {
        console.error('Failed to send DingTalk notification:', response.status);
      }
    } catch (error) {
      console.error('Error sending DingTalk notification:', error);
    }
  }

  private clearDisconnectInterval() {
    if (this.disconnectInterval) {
      clearInterval(this.disconnectInterval);
      this.disconnectInterval = null;
    }
  }

}
