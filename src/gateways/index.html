<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>
<body>
  <script>
    var web = new WebSocket('ws://localhost:3001/pay')
    web.onopen = () => {
      console.log('onopen')
      console.log(arguments)
    }
    web.onmessage = () => {
      console.log('onmessage')
      console.log(arguments)
      web.send({
        event: 'hello',
        data: 'abcxxx'
      })
    }
    web.onclose = () => {
      console.log('onclose')
      console.log(arguments)
    }
    web.onerror = () => {
      console.log('onerror')
      console.log(arguments)
    }
  </script>
</body>
</html>