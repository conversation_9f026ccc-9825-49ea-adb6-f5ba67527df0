import { Module } from "@nestjs/common";
import { GiantModule } from "src/modules/giant/giant.module";
import { PaycodeModule } from "src/modules/paycode/paycode.module";
import { PayconfigModule } from "src/modules/payconfig/payconfig.module";
import { PayAdminGateway } from "./pay-admin.gateway";
import { PayGateway } from "./pay.gateway";
import { PayAdminService } from "./services/pay-admin.service";

@Module({
  imports: [PaycodeModule, PayconfigModule, GiantModule],
  exports: [PayAdminGateway, PayAdminService],
  providers: [PayAdminGateway, PayGateway, PayAdminService]
})
export class PayModule { }