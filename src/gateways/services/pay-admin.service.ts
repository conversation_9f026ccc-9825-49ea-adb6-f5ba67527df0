import { Injectable } from "@nestjs/common";
import { v4 } from "uuid";

@Injectable()
export class PayAdminService {
  private client: any

  getClient() {
    return this.client
  }

  setClient(client: any) {
    this.client = client
  }

  releaseClient() {
    this.client = null
  }

  getPrice(msg: string): number {
    const regex = /(\d+\.?\d*)元/i
    const res = regex.exec(msg)
    if (res) {
      return Number(res[1])
    }
    return 0
  }

}