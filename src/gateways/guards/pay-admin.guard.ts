import { CanActivate, ExecutionContext, Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { GiantDocument } from "src/modules/giant/schemas/giant.schema";
import { GiantService } from "src/modules/giant/services/giant.service";
import { PayAdminService } from "../services/pay-admin.service";

@Injectable()
export class PaySessionGuard implements CanActivate {
  constructor(
    private readonly payAdminService: PayAdminService
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const client = context.switchToWs().getClient()
    const currentClient = this.payAdminService.getClient()
    if (currentClient && currentClient === client) {
      return true
    } else if (!currentClient) {
      this.payAdminService.setClient(client)
      return true
    }
    return false
  }
}
