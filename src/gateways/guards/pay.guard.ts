import { CanActivate, ExecutionContext, Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { GiantDocument } from "src/modules/giant/schemas/giant.schema";
import { GiantService } from "src/modules/giant/services/giant.service";
import { PayAdminService } from "../services/pay-admin.service";

@Injectable()
export class PayGuard implements CanActivate {
  constructor(
    private readonly payAdminService: PayAdminService
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const currentClient = this.payAdminService.getClient()
    if (currentClient) {
      return true
    }
    return false
  }
}
