import { Injectable } from "@nestjs/common";
import { MessageBody, SubscribeMessage, WebSocketGateway, OnGatewayDisconnect, WebSocketServer } from "@nestjs/websockets";
import { GiantService } from "src/modules/giant/services/giant.service";
import { PaycodeService } from "src/modules/paycode/services/paycode.service";
import { PayconfigService } from "src/modules/payconfig/services/payconfig.service";
import { v4 } from "uuid";
import { AuthPay, CanPay } from "./decorators/pay.decorator";
import { PayAdminService } from "./services/pay-admin.service";

@WebSocketGateway({
  path: '/ws/pay',
})
export class PayGateway implements OnGatewayDisconnect {
  @WebSocketServer() private server: any;

  private readonly payMap = new Map<number, {
    key: string,
    days: number,
    client: any,
    timeout: any
  }>()

  constructor(
    private giantService: GiantService,
    private payAdminService: PayAdminService,
    private paycofigService: PayconfigService,
    private paycodeService: PaycodeService
  ) {
  }

  async payFor(price: number) {
    const payInfo = this.payMap.get(price)
    if (!payInfo) {
      console.error('no consistent pay info')
      return ''
    }

    const giant = await this.giantService.createOrUpdateGiant(payInfo.key, payInfo.days)
    payInfo.client.send(JSON.stringify({
      event: 'pay',
      data: {
        key: giant.key,
        days: payInfo.days
      }
    }))
    await this.paycofigService.dicreaseLimit(payInfo.days)
    payInfo.client.close()
    return giant.key
  }

  // @CanPay()
  @SubscribeMessage('code')
  async getCode(client: any, data: { days: number, key: string }) {
    const adminClient = this.payAdminService.getClient()
    if (!adminClient) {
      return {
        event: 'error',
        data: '无法连接到服务器'
      }
    }

    const { price, codeUrl } = await this.paycodeService.assignCode(data.days)
    if (price < 0) {
      client.close()
      return
    }

    const timeout = setTimeout(() => {
      const payInfo = this.payMap.get(price)
      if (payInfo.client === client) {
        client.close()
      }
    }, 5 * 60 * 1000)
    this.payMap.set(price, {
      key: data.key,
      days: data.days,
      client,
      timeout
    })
    return {
      event: 'code',
      data: {
        price,
        codeUrl,
      }
    }
  }

  handleDisconnect(client: any) {
    // find pay map whose client is current
    console.log('disconnect client pay gateway')
    const payInfo = Array.from(this.payMap.entries()).find(([_, v]) => v.client === client)
    if (payInfo) {
      console.log('clear...')
      const price = payInfo[0]
      const { timeout } = this.payMap.get(price)
      this.paycodeService.releaseCode(price)
      clearTimeout(timeout)
      this.payMap.delete(price)
    }

  }

}