import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { marked } from 'marked'
import { HelperCosService } from './helper.cos.service';
import { JSDOM } from 'jsdom';

@Injectable()
export class HelperMd2HtmlService {
  constructor(
    private readonly helperCosService: HelperCosService,
    private readonly configService: ConfigService,
  ) {
    marked.use({
      renderer: {
        image(href, title, text) {
          return `<img src="${href}" alt="${text}" style="max-width: 100%;" />`
        }
      }
    })
  }

  async parse(md: string) {
    const html = await marked(md)
    return html
  }
}
