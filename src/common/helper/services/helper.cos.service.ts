import { Injectable } from '@nestjs/common';
import { IHelperGeoCurrent, IHelperGeoRules } from '../helper.interface';
import COS from "@ultraknown/cos-nodejs-sdk-v5";
import { ConfigService } from '@nestjs/config';

@Injectable()
export class HelperCosService {
    private readonly cos: COS;

    constructor(
        private readonly configService: ConfigService,
    ) {
        this.cos = new COS({
            SecretId: configService.get<string>('cos.secretId'),
            SecretKey: configService.get<string>('cos.secretKey')
        });
    }

    uploadFile(key: string, buffer: Buffer) {
        return this.cos.putObject({
            Bucket: this.configService.get<string>('cos.bucket'),
            Region: this.configService.get<string>('cos.region'),
            Key: key,
            Body: buffer
        })
    }
}
