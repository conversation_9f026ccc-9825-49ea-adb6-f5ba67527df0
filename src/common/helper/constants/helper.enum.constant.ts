export enum ENUM_HELPER_DATE_FORMAT {
    DATE = 'YYYY-MM-DD',
    FRIENDLY_DATE = 'MMM, DD YYYY',
    FRIENDLY_DATE_TIME = 'MMM, DD YYYY HH:MM:SS',
    YEAR_MONTH = 'YYYY-MM',
    MONTH_DATE = 'MM-DD',
    ONLY_YEAR = 'YYYY',
    ONLY_MONTH = 'MM',
    ONLY_DATE = 'DD',
    ISO_DATE = 'YYYY-MM-DDTHH:MM:SSZ',
}

export enum ENUM_HELPER_DATE_DIFF {
    MILIS = 'milis',
    SECONDS = 'seconds',
    HOURS = 'hours',
    DAYS = 'days',
    MINUTES = 'minutes',
}
