import { Injectable } from '@nestjs/common';
import {
    registerDecorator,
    ValidationArguments,
    ValidationOptions,
    ValidatorConstraint,
    ValidatorConstraintInterface
} from 'class-validator';

@ValidatorConstraint({ async: true })
@Injectable()
export class SpecifiedStringConstraint implements ValidatorConstraintInterface {
    validate(value: string, args: ValidationArguments): boolean {
        const [candidates] = args.constraints;
        return candidates.includes(value || '')
    }
}

export function SpecifiedString(candidates: string[], validationOptions?: ValidationOptions) {
    return function (object: Record<string, any>, propertyName: string): any {
        registerDecorator({
            name: 'SpecifiedString',
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [candidates],
            validator: SpecifiedStringConstraint,
        });
    };
}
