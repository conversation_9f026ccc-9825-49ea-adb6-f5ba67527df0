import { CanActivate, ExecutionContext, Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { IGiantDocument } from "src/modules/giant/giant.interface";
import { GiantDocument } from "src/modules/giant/schemas/giant.schema";
import { GiantService } from "src/modules/giant/services/giant.service";

@Injectable()
export class GiantGuard implements CanActivate {
  constructor(
    private readonly giantService: GiantService
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const key = request.headers['key']
    const giant: GiantDocument = await this.giantService.findByKey(key)
    if (giant && !giant.disabled) {
      request.__giant = giant;
      request.__key = key
      return true;
    }
    return false
  }
}

@Injectable()
export class GiantWeakGuard implements CanActivate {
  constructor(
    private readonly giantService: GiantService
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const key = request.headers['key']
    const giant: GiantDocument = await this.giantService.findByKey(key)
    if (giant && !giant.disabled) {
      request.__giant = giant;
      request.__key = key
      return true;
    }
    request.__giantError = 4031
    return true
  }
}

@Injectable()
export class GiantMachineExceedGuard implements CanActivate {
  constructor(
    private readonly giantService: GiantService
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const machineId = request.headers['machineid']
    const machineName = decodeURIComponent(request.headers['machinename'] || '')
    if (!machineId) return false
    request.__machineId = machineId

    const giant = request.__giant as IGiantDocument
    const currentMachine = giant.machines.find(m => m.id === machineId)
    if (currentMachine) {
      if (machineName && currentMachine.name !== machineName) {
        await this.giantService.updateMachine(giant, {
          id: currentMachine.id,
          name: machineName
        })
      }
      return true
    }
    if (giant.machines.length < 2) {
      await this.giantService.increment(giant, {
        id: machineId,
        name: machineName || 'Anonymouse'
      })
      return true
    }
    return false
  }
}

@Injectable()
export class GiantMachineExceedWeakGuard implements CanActivate {
  constructor(
    private readonly giantService: GiantService
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    if (request.__giantError) {
      return true
    }

    const machineId = request.headers['machineid']
    const machineName = decodeURIComponent(request.headers['machinename'] || '')
    if (!machineId) {
      request.__giantError = 4032
      return true
    }
    request.__machineId = machineId

    const giant = request.__giant as IGiantDocument
    const currentMachine = giant.machines.find(m => m.id === machineId)
    if (currentMachine) {
      if (machineName && currentMachine.name !== machineName) {
        await this.giantService.updateMachine(giant, {
          id: currentMachine.id,
          name: machineName
        })
      }
      return true
    }
    if (giant.machines.length < 2) {
      await this.giantService.increment(giant, {
        id: machineId,
        name: machineName || 'Anonymouse'
      })
      return true
    }
    request.__giantError = 4033
    return true
  }
}

@Injectable()
export class GiantOptionalGuard implements CanActivate {
  constructor(
    private readonly giantService: GiantService
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const key = request.headers['key']
    const machineId = request.headers['machineid']
    request.__machineId = machineId
    const giant: GiantDocument = await this.giantService.findByKey(key)
    if (giant && !giant.disabled) {
      request.__giant = giant;
      request.__key = key
      return true;
    }
    return true
  }
}

