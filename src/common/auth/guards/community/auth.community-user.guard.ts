import { CanActivate, ExecutionContext, Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { SyncService } from "src/modules/community/sync/services/sync.service";
import { IGiantDocument } from "src/modules/giant/giant.interface";
import { GiantDocument } from "src/modules/giant/schemas/giant.schema";
import { GiantService } from "src/modules/giant/services/giant.service";

@Injectable()
export class CommunityUserGuard implements CanActivate {
  constructor(
    private readonly syncService: SyncService
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const user = await this.syncService.validate(request.body)
    if (user) {
      request.__user = user;
      return true
    }

    return false
  }
}
