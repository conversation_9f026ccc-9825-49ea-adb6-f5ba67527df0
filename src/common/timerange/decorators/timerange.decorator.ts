import { applyDecorators } from "@nestjs/common";
import { Expose, Transform } from "class-transformer";
import { isArray, IsObject, IsOptional } from "class-validator";

export function TimeRange(): any {
  return applyDecorators(
    Expose(),
    IsOptional(),
    IsObject(),
    Transform(({ key, value }) => {
      if (isArray(value)) {
        return {
          $gt: value[0],
          $lt: value[1]
        }
      }
      return value
    })
  );
}

