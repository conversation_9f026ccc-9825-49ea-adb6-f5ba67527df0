import { IErrorHttpFilterMetadata } from 'src/common/error/error.interface';
import { IMessage } from 'src/common/message/message.interface';

export class ResponsePagingMetadataDto {
    nextPage?: string;
    previousPage?: string;
    firstPage?: string;
    lastPage?: string;
}

export class ResponsePagingDto<T = Record<string, any>> {
    readonly statusCode: number;
    readonly message: string | IMessage;
    readonly total: number;
    totalPage?: number;
    current?: number;
    pageSize?: number;
    availableSearch?: string[];
    availableSort?: string[];
    readonly metadata?: IErrorHttpFilterMetadata & ResponsePagingMetadataDto;
    readonly data?: T[];
}
