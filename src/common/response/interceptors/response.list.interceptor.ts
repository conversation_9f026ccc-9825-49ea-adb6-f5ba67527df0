import {
    Call<PERSON><PERSON><PERSON>, ExecutionContext, Injectable,
    NestInterceptor
} from '@nestjs/common';
import { HttpArgumentsHost } from '@nestjs/common/interfaces';
import { Reflector } from '@nestjs/core';
import {
    ClassConstructor,
    ClassTransformOptions,
    plainToInstance
} from 'class-transformer';
import { Response } from 'express';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { IErrorHttpFilterMetadata } from 'src/common/error/error.interface';
import {
    IMessage,
    IMessageOptionsProperties
} from 'src/common/message/message.interface';
import { MessageService } from 'src/common/message/services/message.service';
import { IRequestApp } from 'src/common/request/request.interface';
import {
    RESPONSE_MESSAGE_PATH_META_KEY, RESPONSE_MESSAGE_PROPERTIES_META_KEY,
    RESPONSE_SERIALIZATION_META_KEY,
    RESPONSE_SERIALIZATION_OPTIONS_META_KEY
} from '../constants/response.constant';
import { ResponseListDto } from '../dtos/response.list.dto';
import { IResponseList } from '../response.interface';

@Injectable()
export class ResponseListInterceptor
    implements NestInterceptor<Promise<any>>
{
    constructor(
        private readonly reflector: Reflector,
        private readonly messageService: MessageService
    ) { }

    async intercept(
        context: ExecutionContext,
        next: CallHandler
    ): Promise<Observable<Promise<ResponseListDto>>> {
        if (context.getType() === 'http') {
            return next.handle().pipe(
                map(async (responseData: Promise<ResponseListDto>) => {
                    const ctx: HttpArgumentsHost = context.switchToHttp();
                    const responseExpress: Response = ctx.getResponse();
                    const requestExpress: IRequestApp =
                        ctx.getRequest<IRequestApp>();

                    let messagePath: string = this.reflector.get<string>(
                        RESPONSE_MESSAGE_PATH_META_KEY,
                        context.getHandler()
                    );

                    const classSerialization: ClassConstructor<any> =
                        this.reflector.get<ClassConstructor<any>>(
                            RESPONSE_SERIALIZATION_META_KEY,
                            context.getHandler()
                        );
                    const classSerializationOptions: ClassTransformOptions =
                        this.reflector.get<ClassTransformOptions>(
                            RESPONSE_SERIALIZATION_OPTIONS_META_KEY,
                            context.getHandler()
                        );
                    const messageProperties: IMessageOptionsProperties =
                        this.reflector.get<IMessageOptionsProperties>(
                            RESPONSE_MESSAGE_PROPERTIES_META_KEY,
                            context.getHandler()
                        );

                    // message base on language
                    const { customLang } = ctx.getRequest<IRequestApp>();

                    // response
                    const response = (await responseData) as IResponseList;
                    const {
                        metadata,
                        data,
                    } = response;
                    let statusCode: number = responseExpress.statusCode;
                    let properties: IMessageOptionsProperties =
                        messageProperties;
                    let serialization = data;

                    if (classSerialization) {
                        serialization = plainToInstance(
                            classSerialization,
                            data,
                            classSerializationOptions
                        );
                    }

                    // get metadata
                    const __path = requestExpress.path;
                    const __requestId = requestExpress.id;
                    const __timestamp = requestExpress.timestamp;
                    const __timezone = requestExpress.timezone;
                    const __version = requestExpress.version;
                    const __repoVersion = requestExpress.repoVersion;

                    if (metadata) {
                        statusCode = metadata.statusCode || statusCode;
                        messagePath = metadata.message || messagePath;
                        properties = metadata.properties || properties;

                        delete metadata.statusCode;
                        delete metadata.message;
                        delete metadata.properties;
                    }

                    const resMetadata: IErrorHttpFilterMetadata = {
                        languages: customLang,
                        timestamp: __timestamp,
                        timezone: __timezone,
                        requestId: __requestId,
                        path: __path,
                        version: __version,
                        repoVersion: __repoVersion,
                    };

                    // message
                    const message: string | IMessage =
                        await this.messageService.get(messagePath, {
                            customLanguages: customLang,
                            properties,
                        });

                    const responseHttp: ResponseListDto = {
                        statusCode,
                        message,
                        metadata: {
                            ...resMetadata,
                            ...metadata,
                        },
                        data: serialization,
                    };

                    return responseHttp;
                })
            );
        }

        return next.handle();
    }
}
