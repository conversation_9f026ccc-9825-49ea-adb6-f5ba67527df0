import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createTransport } from 'nodemailer';

type Mail = {
  host: string
  port: number
  from: string
  sender: string
  auth: {
    user: string
    pass: string
  }
}

@Injectable()
export class MailService {
  private availableMailList: Mail[] = []
  private mailInfo: Mail

  constructor(private readonly configService: ConfigService) {
    // this.availableMailList = [
    //   {
    //     host: 'smtp.office365.com',
    //     port: 587,
    //     auth: {
    //       user: '<EMAIL>',
    //       pass: '6Qbbr3qq'
    //     }
    //   }
    // ]

    this.mailInfo = {
      host: this.configService.get<string>('mail.host'),
      port: this.configService.get<number>('mail.port'),
      from: this.configService.get<string>('mail.from'),
      sender: this.configService.get<string>('mail.sender'),
      auth: {
        user: this.configService.get<string>('mail.user'),
        pass: this.configService.get<string>('mail.pass'),
      },
    }
  }

  async sendToMail(to: string, subject, html: string) {

    try {
      this.trySendMail(this.mailInfo, to, subject, html)
    } catch (error) {
      console.error(`send mail failed: ${this.mailInfo.auth.user} -> ${to}`)
      console.error(error)
    }
  }

  // async sendToMail(to: string, subject, html: string) {
  //   for (const mail of this.availableMailList) {
  //     try {
  //       await this.trySendMail(mail, to, subject, html)
  //       break
  //     } catch (error) {
  //       console.error(`send mail failed: ${mail.auth.user} -> ${to}`)
  //       console.error(error)
  //     }
  //   }
  // }

  // async sendKeyToMail(host: string, to: string, key: string) {
  //   for (const mail of this.availableMailList) {
  //     try {
  //       const html = `<b>您的秘钥是${key}, 请保存好,勿泄露</b>`
  //       await this.trySendMail(mail, to, `感谢购买海妖 ✔`, html)
  //     } catch (error) {
  //       console.error(`send mail failed: ${mail.auth.user} -> ${to}`)
  //       console.error(error)
  //     }
  //   }
  // }

  async trySendMail(mail: Mail, to: string, subject: string, html: string) {
    // create reusable transporter object using the default SMTP transport
    const transporter = createTransport({
      host: mail.host,
      port: mail.port,
      secure: true,
      auth: {
        user: mail.auth.user,
        pass: mail.auth.pass
      },
    });

    // send mail with defined transport object
    const info = await transporter.sendMail({
      from: `"Kraken Team" <${mail.auth.user}>`, // sender address
      sender: mail.sender,
      to,
      subject,
      // subject: "感谢购买海妖 ✔", // Subject line
      // text: "Hello jjjj?", // plain text body
      html, // html body
    });

    console.log('send mail success: ', to)
  }

}
