import { Injectable } from '@nestjs/common';
import {
    PAGINATION_MAX_PAGE,
    PAGINATION_MAX_PER_PAGE,
} from '../constants/pagination.constant';

@Injectable()
export class PaginationService {
    async skip(current: number, pageSize: number): Promise<number> {
        current = current > PAGINATION_MAX_PAGE ? PAGINATION_MAX_PAGE : current;
        pageSize =
            pageSize > PAGINATION_MAX_PER_PAGE
                ? PAGINATION_MAX_PER_PAGE
                : pageSize;
        const skip: number = (current - 1) * pageSize;
        return skip;
    }

    async totalPage(total: number, limit: number): Promise<number> {
        let totalPage = Math.ceil(total / limit);
        totalPage = totalPage === 0 ? 1 : totalPage;
        return totalPage > PAGINATION_MAX_PAGE
            ? PAGINATION_MAX_PAGE
            : totalPage;
    }
}
