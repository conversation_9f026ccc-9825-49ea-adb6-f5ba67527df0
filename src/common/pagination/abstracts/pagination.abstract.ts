import { IPaginationSort } from '../pagination.interface';

export abstract class PaginationListAbstract {
    abstract search?: Record<string, any>;
    abstract availableSearch?: string[];
    abstract current?: number;
    abstract pageSize: number;
    abstract sort?: IPaginationSort;
    abstract availableSort?: string[];
}

export abstract class PaginationSimpleListAbstract {
    abstract search?: string;
    abstract current?: number;
    abstract pageSize: number;
}

export abstract class PaginationMiniListAbstract {
    abstract pageSize: number;
}
