import { Body, Controller, Get, Param, Post, Put, Query } from '@nestjs/common';
import { PaginationService } from 'src/common/pagination/services/pagination.service';
import { RequestParamGuard } from 'src/common/request/decorators/request.decorator';
import {
  Response,
  ResponsePaging,
} from 'src/common/response/decorators/response.decorator';
import {
  IResponse,
  IResponsePaging,
} from 'src/common/response/response.interface';
import { GetSetting } from '../decorators/setting.decorator';
import {
  SettingGetByNameGuard,
  SettingGetGuard,
} from '../decorators/setting.public.decorator';
import { SettingCreateDto } from '../dtos/setting.create.dto';
import { SettingListDto } from '../dtos/setting.list.dto';
import { SettingRequestDto } from '../dtos/setting.request.dto';
import { SettingUpdateDto } from '../dtos/setting.update.dto';
import { SettingDocument } from '../schemas/setting.schema';
import { SettingGetSerialization } from '../serializations/setting.get.serialization';
import { SettingListSerialization } from '../serializations/setting.list.serialization';
import { SettingService } from '../services/setting.service';
import { AuthAdminJwtGuard } from 'src/common/auth/decorators/auth.jwt.decorator';

@AuthAdminJwtGuard()
@Controller({
  version: '1',
  path: '/setting',
})
export class SettingController {
  constructor(
    private readonly settingService: SettingService,
    private readonly paginationService: PaginationService
  ) { }

  @ResponsePaging('setting.list', {
    classSerialization: SettingListSerialization,
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      sort,
      search,
      availableSort,
      availableSearch,
    }: SettingListDto
  ): Promise<IResponsePaging> {
    const skip: number = await this.paginationService.skip(current, pageSize);
    const find: Record<string, any> = {
      ...search,
    };

    const settings: SettingDocument[] = await this.settingService.findAll(
      find,
      {
        limit: pageSize,
        skip: skip,
        sort,
      }
    );
    const total: number = await this.settingService.getTotal(find);
    const totalPage: number = await this.paginationService.totalPage(
      total,
      pageSize
    );

    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: settings,
    };
  }

  @Response('setting.get', {
    classSerialization: SettingGetSerialization,
  })
  @SettingGetGuard()
  @RequestParamGuard(SettingRequestDto)
  @Get('get/:setting')
  async get(@GetSetting() setting: SettingDocument): Promise<IResponse> {
    return setting;
  }

  @Response('setting.getByName', {
    classSerialization: SettingGetSerialization,
  })
  @SettingGetByNameGuard()
  @Get('get/name/:settingName')
  async getByName(
    @GetSetting() setting: SettingDocument
  ): Promise<IResponse> {
    return setting;
  }

  @Response('setting.create')
  @Post('/create')
  async create(
    @Body()
    settingCreateDto: SettingCreateDto
  ) {
    return this.settingService.create(settingCreateDto)
  }

  @Response('setting.update')
  @Put('/update/:id')
  async update(
    @Param('id')
    id: string,
    @Body()
    settingUpdateDto: SettingUpdateDto
  ) {
    return this.settingService.updateOneById(id, settingUpdateDto)
  }
}
