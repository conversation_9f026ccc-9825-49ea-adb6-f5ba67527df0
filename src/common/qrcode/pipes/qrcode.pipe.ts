import {
  PipeTransform,
  Injectable,
  PayloadTooLargeException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ENUM_FILE_STATUS_CODE_ERROR } from 'src/common/file/constants/file.status-code.constant';
import { IFile } from 'src/common/file/file.interface';
import <PERSON><PERSON> from "jimp";
import Qrcode from "qrcode-reader";

@Injectable()
export class QrcodeReadPipe implements PipeTransform {
  async transform(value: IFile | IFile[]): Promise<string | string[]> {
      debugger
      if (!value) {
          return;
      }

      if (Array.isArray(value)) {
        const results = await Promise.all(value.map(val => this.get(val.buffer)))
        return results
      }

      const file: IFile = value as IFile;
      const result = await this.get(file.buffer);
      return result
  }
  async get(buffer: Buffer): Promise<string> {
    return new Promise((resolve, reject) => {
      Jimp.read(buffer, (err, image) => {
        if (err) {
          reject(err)
        }
        const qrcode = new Qrcode()
        qrcode.callback = (err, value) => {
          if (err) {
            reject(err)
          }
          resolve(value.result)
        }
        qrcode.decode(image.bitmap)
      })
    })
  }
}
