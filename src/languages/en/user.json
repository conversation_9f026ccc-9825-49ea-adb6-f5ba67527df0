{"list": "List User Success.", "get": "Get User Success.", "create": "Create User Success.", "delete": "Delete User Success.", "update": "Update User Success.", "profile": "Profile Success", "upload": "Upload Success", "inactive": "Inactive Succeed", "active": "Active Succeed", "login": "Login success.", "refresh": "Refresh token success", "signUp": "Sign up Success", "error": {"notFound": "User not found.", "emailExist": "Email user used", "mobileNumberExist": "Mobile Number user used", "active": "User status active invalid", "exist": "User exist", "passwordNotMatch": "Password not match", "newPasswordMustDifference": "Old password must difference", "inactive": "User is inactive", "passwordExpired": "User password expired"}}