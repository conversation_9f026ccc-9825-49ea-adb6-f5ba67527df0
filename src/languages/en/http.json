{"success": {"ok": "OK", "created": "Created", "accepted": "Accepted", "noContent": "No Content"}, "redirection": {"movePermanently": "Move Permanently", "found": "Found", "notModified": "Not Modified", "temporaryRedirect": "Temporary Redirect", "permanentRedirect": "Permanent Redirect"}, "clientError": {"badRequest": "Bad Request", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "notFound": "Not Found", "methodNotAllowed": "Not Allowed Method", "notAcceptable": "Not Acceptable", "payloadToLarge": "Payload To Large", "uriToLarge": "<PERSON><PERSON>", "unsupportedMediaType": "Unsupported Media Type", "unprocessableEntity": "Unprocessable Entity", "tooManyRequest": "Too Many Request"}, "serverError": {"internalServerError": "Internal Server Error", "notImplemented": "Not Implemented", "badGateway": "Bad Gateway", "serviceUnavailable": "Service Unavailable", "gatewayTimeout": "Gateway Timeout"}, "200": "OK", "201": "Created", "202": "Accepted", "204": "No Content", "301": "Move Permanently", "302": "Found", "304": "Not Modified", "307": "Temporary Redirect", "308": "Permanent Redirect", "400": "Bad Request", "401": "Unauthorized", "403": "Forbidden", "404": "Not Found", "405": "Not Allowed Method", "406": "Not Acceptable", "413": "Payload To Large", "414": "<PERSON><PERSON>", "415": "Unsupported Media Type", "422": "Unprocessable Entity", "429": "Too Many Request", "500": "Internal Server Error", "501": "Not Implemented", "502": "Bad Gateway", "503": "Service Unavailable", "504": "Gateway Timeout"}