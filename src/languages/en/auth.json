{"enum": {"accessFor": "Enum access for succeed"}, "error": {"roleBlocked": "Role blocked", "blocked": "User Blocked", "passwordExpired": "Password expired, go reset password", "permissionForbidden": "Permission not allowed", "accessForForbidden": "Access for not allowed"}, "apiKey": {"error": {"keyNeeded": "Need API Key", "prefixInvalid": "Prefix API Key invalid", "schemaInvalid": "API Key Schema invalid", "timestampNotMatchWithRequest": "Timestamp not match with request", "notFound": "Auth API not found", "inactive": "Auth API Inactive", "invalid": "Invalid API Key"}}}