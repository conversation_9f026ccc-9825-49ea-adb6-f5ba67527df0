import { registerAs } from '@nestjs/config';

export default registerAs(
    'communityDatabase',
    (): Record<string, any> => ({
        host: process.env.COMMUNITY_DATABASE_HOST || 'mongodb://localhost:27017',
        name: process.env.COMMUNITY_DATABASE_NAME || 'k-community',
        user: process.env.COMMUNITY_DATABASE_USER || null,
        password: process.env.COMMUNITY_DATABASE_PASSWORD || null,
        debug: process.env.COMMUNITY_DATABASE_DEBUG === 'true' || false,
        options: process.env.COMMUNITY_DATABASE_OPTIONS,
    })
);
