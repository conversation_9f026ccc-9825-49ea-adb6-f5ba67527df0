import { registerAs } from '@nestjs/config';

export default registerAs(
  'download',
  (): Record<string, any> => ({
    staticPrefix: process.env.STATIC_PREFIX || '/release',
    staticPath: process.env.STATIC_BASE_PATH || '/var/www/html/static',
    basePath: process.env.DOWNLOAD_BASE_PATH || 'kraken/download',
    appPath: process.env.DOWNLOAD_APP_PATH || 'app',
    appReadyPath: process.env.DOWNLOAD_APP_READY_PATH || 'app-ready',
    appMapPath: process.env.DOWNLOAD_APP_MAP_PATH || 'app-map',
    extensionPath: process.env.DOWNLOAD_EXTENSION_PATH || 'extension',
    browserExtensionPath: process.env.DOWNLOAD_BROWSER_EXTENSION_PATH || 'browser-extension',
    browserExtensionCompatPath: process.env.DOWNLOAD_BROWSER_EXTENSION_COMPAT_PATH || 'browser-extension-compat',
    versionPath: process.env.DOWNLOAD_VERSION_PATH || 'version',
    browserExtensionVersionPath: process.env.DOWNLOAD_BROWSER_EXTENSION_VERSION_PATH || 'browser-extension-version',
    appHomePath: process.env.APP_HOME_PATH || 'app-home',
    domain: process.env.DOWNLOAD_DOMAIN || 'http://static.ultraknown.com',
  })
);
