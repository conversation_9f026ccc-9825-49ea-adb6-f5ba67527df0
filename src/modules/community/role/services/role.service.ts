import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { FilterQuery, Model } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { RoleCreateDto } from "../dtos/role.create.dto";
import { RoleUpdateDto } from "../dtos/role.update.dto";
import { IRoleDocument } from "../role.interface";
import { RoleDocument, RoleEntity } from "../schemas/role.schema";
import { COMMUNITY_DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";

@Injectable()
export class RoleService {
  constructor(
    @DatabaseEntity(RoleEntity.name, COMMUNITY_DATABASE_CONNECTION_NAME)
    private readonly roleModel: Model<RoleDocument>,
  ) {
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IRoleDocument[]> {
    const roles = this.roleModel.find(find)
    if (
      options &&
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      roles.limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      roles.sort(options.sort)
    }

    return roles.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.roleModel.countDocuments(find)
  }

  async exists(name: string) {
    const exists = await this.roleModel.exists({
      name,
    });
    return !!exists;
  }

  async findOne(filterQuery: FilterQuery<RoleDocument>) {
    const role = await this.roleModel.findOne(filterQuery);
    return role;
  }

  async create(data: RoleCreateDto) {
    const create: RoleDocument = new this.roleModel(data);
    return create.save();
  }

  async deleteById(id: string) {
    return this.roleModel.findByIdAndDelete(id)
  }

  async updateOneById(_id: string, roleUpdateDto: RoleUpdateDto, timezone: string) {
    const update: RoleDocument = await this.roleModel.findById(_id);
    if (!update) {
      throw new InternalServerErrorException('Role not found')
    }
    // update document by _id, replace by roleUpdateDto
    update.set(roleUpdateDto)
    return update.save()
  }

}