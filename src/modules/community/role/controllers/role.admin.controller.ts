import { BadRequestException, Body, Controller, Delete, Get, InternalServerErrorException, Param, Post, Put, Query } from "@nestjs/common";
import { AuthJwtGuard } from "src/common/auth/decorators/auth.jwt.decorator";
import { ENUM_ERROR_STATUS_CODE_ERROR } from "src/common/error/constants/error.status-code.constant";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { RequestTimezone } from "src/common/request/decorators/request.decorator";
import { Response, ResponseList, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { ENUM_GIANT_STATUS_CODE_ERROR } from "../constants/role.status-code.constant";
import { RoleCreateDto } from "../dtos/role.create.dto";
import { RoleListDto } from "../dtos/role.list.dto";
import { RoleUpdateDto } from "../dtos/role.update.dto";
import { RoleListSerializtion } from "../serializations/role.list.serialization";
import { RoleService } from "../services/role.service";

@AuthJwtGuard()
@Controller('/role')
export class RoleController {
  constructor(
    private readonly roleService: RoleService,
    private readonly paginationService: PaginationService,
  ) { }


  @ResponsePaging('role.list', {
    classSerialization: RoleListSerializtion
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      fields,
      sort,
      search,
      availableSort,
      availableSearch,
    }: RoleListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = Object.assign({
      ...fields
    })

    const roles = await this.roleService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })
    const total = await this.roleService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: roles
    }
  }

  @ResponseList('role.select', {
    classSerialization: RoleListSerializtion
  })
  @Get('/select')
  async select() {
    const data = await this.roleService.findAll();
    return {
      data
    };
  }

  @Response('role.create')
  @Post('/create')
  async create(@Body() roleCreateDto: RoleCreateDto) {
    const exists = await this.roleService.exists(roleCreateDto.name);
    if (exists) {
      throw new BadRequestException({
        statusCode: ENUM_GIANT_STATUS_CODE_ERROR.GIANT_EXIST_ERROR,
        message: 'role.error.exist',
      });
    }

    try {
      const create = await this.roleService.create(roleCreateDto);
      return create;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('role.update')
  @Put('/update/:id')
  async update(
    @Param('id') id: string,
    @Body() roleUpdateDto: RoleUpdateDto,
    @RequestTimezone() timezone: string
  ) {
    try {
      await this.roleService.updateOneById(id, roleUpdateDto, timezone);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('role.delete')
  @Delete('/delete/:id')
  async delete(
    @Param('id') id: string
  ) {
    try {
      return await this.roleService.deleteById(id);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }
}