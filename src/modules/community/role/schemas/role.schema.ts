import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, ObjectId, Types } from 'mongoose';
import { v4 } from "uuid";
import { PermissionEntity } from '../../permission/schemas/permission.schema';

@Schema({ timestamps: true, versionKey: false })
export class RoleEntity {
  @Prop({
    required: true,
  })
  name: string;

  @Prop({
    default: () => [],
    type: [{
      type: Types.ObjectId,
      ref: PermissionEntity.name,
    }],
    set: (value: (null | string | ObjectId)[]) => {
      return value?.map((item) => {
        if (typeof item === 'string') {
          return new Types.ObjectId(item);
        }
        return item
      }) || []
    }
  })
  permissions: Types.ObjectId[];
}

export const RoleDatabaseName = 'roles';
export const RoleSchema = SchemaFactory.createForClass(RoleEntity);

export type RoleDocument = RoleEntity & Document;
