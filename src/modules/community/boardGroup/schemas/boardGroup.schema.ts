import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { BoardEntity } from '../../board/schemas/board.schema';

@Schema({ timestamps: true, versionKey: false })
export class BoardGroupEntity {
  @Prop({
    required: true,
  })
  title: string;

  @Prop({
    default: '',
  })
  description: string;

  @Prop({
    type: [{
      type: Types.ObjectId,
      ref: BoardEntity.name,
    }],
    set: (value: (null | string | Types.ObjectId)[]) => {
      return value?.map((item) => {
        if (typeof item === 'string') {
          return new Types.ObjectId(item);
        }
        return item
      }) || []
    }
  })
  boards: Types.ObjectId[];

  @Prop({
    type: Number,
    default: 0,
  })
  order: number
}

export const BoardGroupDatabaseName = 'boardgroups';
export const BoardGroupSchema = SchemaFactory.createForClass(BoardGroupEntity);

export type BoardGroupDocument = BoardGroupEntity & Document;
