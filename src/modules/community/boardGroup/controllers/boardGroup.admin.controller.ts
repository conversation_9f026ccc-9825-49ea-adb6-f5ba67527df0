import { BadRequestException, Body, Controller, Delete, Get, InternalServerErrorException, Param, Post, Put, Query } from "@nestjs/common";
import { AuthJwtGuard } from "src/common/auth/decorators/auth.jwt.decorator";
import { ENUM_ERROR_STATUS_CODE_ERROR } from "src/common/error/constants/error.status-code.constant";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { RequestTimezone } from "src/common/request/decorators/request.decorator";
import { Response, ResponseList, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { ENUM_GIANT_STATUS_CODE_ERROR } from "../constants/boardGroup.status-code.constant";
import { BoardGroupCreateDto } from "../dtos/boardGroup.create.dto";
import { BoardGroupListDto } from "../dtos/boardGroup.list.dto";
import { BoardGroupUpdateDto } from "../dtos/boardGroup.update.dto";
import { BoardGroupListSerializtion } from "../serializations/boardGroup.list.serialization";
import { BoardGroupService } from "../services/boardGroup.service";

@AuthJwtGuard()
@Controller('/boardGroup')
export class BoardGroupController {
  constructor(
    private readonly boardGroupService: BoardGroupService,
    private readonly paginationService: PaginationService,
  ) { }


  @ResponsePaging('boardGroup.list', {
    classSerialization: BoardGroupListSerializtion
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      fields,
      sort,
      search,
      availableSort,
      availableSearch,
    }: BoardGroupListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = Object.assign({
      ...fields
    })

    const boardGroups = await this.boardGroupService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })
    const total = await this.boardGroupService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: boardGroups
    }
  }

  @ResponseList('boardGroup.select', {
    classSerialization: BoardGroupListSerializtion
  })
  @Get('/select')
  async select() {
    const data = await this.boardGroupService.findAll();
    return {
      data
    };
  }

  @Response('boardGroup.create')
  @Post('/create')
  async create(@Body() boardGroupCreateDto: BoardGroupCreateDto) {
    try {
      const create = await this.boardGroupService.create(boardGroupCreateDto);
      return create;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('boardGroup.update')
  @Put('/update/:id')
  async update(
    @Param('id') id: string,
    @Body() boardGroupUpdateDto: BoardGroupUpdateDto,
    @RequestTimezone() timezone: string
  ) {
    try {
      await this.boardGroupService.updateOneById(id, boardGroupUpdateDto, timezone);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('boardGroup.delete')
  @Delete('/delete/:id')
  async delete(
    @Param('id') id: string
  ) {
    try {
      return await this.boardGroupService.deleteById(id);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }
}