import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { FilterQuery, Model, Types } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { BoardGroupCreateDto } from "../dtos/boardGroup.create.dto";
import { BoardGroupUpdateDto } from "../dtos/boardGroup.update.dto";
import { IBoardGroupDocument } from "../boardGroup.interface";
import { BoardGroupDocument, BoardGroupEntity } from "../schemas/boardGroup.schema";
import { COMMUNITY_DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";

@Injectable()
export class BoardGroupService {
  constructor(
    @DatabaseEntity(BoardGroupEntity.name, COMMUNITY_DATABASE_CONNECTION_NAME)
    private readonly boardGroupModel: Model<BoardGroupDocument>,
  ) {
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IBoardGroupDocument[]> {
    const boardGroups = this.boardGroupModel.find(find)
    if (
      options &&
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      boardGroups.limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      boardGroups.sort(options.sort)
    }

    return boardGroups.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.boardGroupModel.countDocuments(find)
  }

  async findOne(filterQuery: FilterQuery<BoardGroupDocument>) {
    const boardGroup = await this.boardGroupModel.findOne(filterQuery);
    return boardGroup;
  }

  async create(data: BoardGroupCreateDto) {
    const create: BoardGroupDocument = new this.boardGroupModel(data);
    return create.save();
  }

  async deleteById(id: string) {
    return this.boardGroupModel.findByIdAndDelete(id)
  }

  async updateOneById(_id: string, boardGroupUpdateDto: BoardGroupUpdateDto, timezone: string) {
    const update: BoardGroupDocument = await this.boardGroupModel.findById(_id);
    if (!update) {
      throw new InternalServerErrorException('BoardGroup not found')
    }
    // update document by _id, replace by boardGroupUpdateDto
    update.set(boardGroupUpdateDto)
    return update.save()
  }

}