// import { Modu<PERSON> } from "@nestjs/common";
// import { MongooseModule } from "@nestjs/mongoose";
// import { DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
// import { MailModule } from "src/common/mail/mail.module";
// import { MailBindGateway } from "./gateways/mail_bind.gateway";
// import { GiantDatabaseName, GiantEntity, GiantSchema } from "./schemas/giant.schema";
// import { InviteDatabaseName, InviteEntity, InviteSchema } from "./schemas/invite.schema";
// import { MailBindDatabaseName, MailBindEntity, MailBindSchema } from "./schemas/mail_bind.schema";
// import { MailFindEntity, MailFindSchema, MailFindDatabaseName } from "./schemas/mail_find.schema";
// import { GiantService } from "./services/giant.service";
// import { InviteService } from "./services/invite.service";
// import { MailBindService } from "./services/mail_bind.service";
// import { MailFindService } from "./services/mail_find.service";

import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { COMMUNITY_DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { BoardGroupService } from "./services/boardGroup.service";
import { BoardGroupDatabaseName, BoardGroupEntity, BoardGroupSchema } from "./schemas/boardGroup.schema";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: BoardGroupEntity.name,
          schema: BoardGroupSchema,
          collection: BoardGroupDatabaseName
        },
      ],
      COMMUNITY_DATABASE_CONNECTION_NAME
    )
  ],
  exports: [BoardGroupService],
  providers: [BoardGroupService],
  controllers: []
})
export class BoardGroupModule { }