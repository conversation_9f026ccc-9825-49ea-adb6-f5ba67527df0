import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { FilterQuery, Model, Types } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { DictCreateDto } from "../dtos/dict.create.dto";
import { DictUpdateDto } from "../dtos/dict.update.dto";
import { IDictDocument } from "../dict.interface";
import { DictDocument, DictEntity } from "../schemas/dict.schema";
import { COMMUNITY_DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";

@Injectable()
export class DictService {
  constructor(
    @DatabaseEntity(DictEntity.name, COMMUNITY_DATABASE_CONNECTION_NAME)
    private readonly dictModel: Model<DictDocument>,
  ) {
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IDictDocument[]> {
    const dicts = this.dictModel.find(find)
    if (
      options &&
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      dicts.limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      dicts.sort(options.sort)
    }

    return dicts.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.dictModel.countDocuments(find)
  }

  async exists(dto: DictCreateDto) {
    const exists = await this.dictModel.exists({
      name: dto.name,
      value: dto.value,
    });
    return !!exists;
  }

  async findOne(filterQuery: FilterQuery<DictDocument>) {
    const dict = await this.dictModel.findOne(filterQuery);
    return dict;
  }

  async create(data: DictCreateDto) {
    const create: DictDocument = new this.dictModel({
      ...data,
      permission: data.permission ? new Types.ObjectId(data.permission) : null
    });
    return create.save();
  }

  async deleteById(id: string) {
    return this.dictModel.findByIdAndDelete(id)
  }

  async updateOneById(_id: string, dictUpdateDto: DictUpdateDto, timezone: string) {
    const update: DictDocument = await this.dictModel.findById(_id);
    if (!update) {
      throw new InternalServerErrorException('Dict not found')
    }
    // update document by _id, replace by dictUpdateDto
    update.set(dictUpdateDto)
    return update.save()
  }

}