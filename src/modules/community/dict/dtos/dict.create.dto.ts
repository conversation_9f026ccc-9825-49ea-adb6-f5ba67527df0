import { IsOptional, IsString } from "class-validator";

export class DictCreateDto {
  @IsString()
  readonly name: string

  // value: string
  @IsString()
  readonly value: string

  // label: string
  @IsString()
  readonly label: string

  @IsString()
  @IsOptional()
  readonly icon: string

  @IsString()
  @IsOptional()
  readonly color: string

  @IsString()
  @IsOptional()
  readonly permission: string
}