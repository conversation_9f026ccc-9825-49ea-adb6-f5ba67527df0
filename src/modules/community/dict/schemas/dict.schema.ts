import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, ObjectId, Types } from 'mongoose';
import { PermissionEntity } from '../../permission/schemas/permission.schema';

@Schema({ timestamps: true, versionKey: false })
export class DictEntity {
  @Prop({
    required: true,
  })
  value: string;

  @Prop({
    required: true,
  })
  label: string;

  @Prop({
    type: String
  })
  icon: string;

  @Prop({
    type: String
  })
  color: string;

  // name
  @Prop({
    required: true,
  })
  name: string;

  @Prop({
    type: Types.ObjectId,
    ref: PermissionEntity.name,
    set: (value: string | ObjectId) => {
      if (typeof value === 'string') {
        return new Types.ObjectId(value);
      }
      return value
    }
  })
  permission: Types.ObjectId;
}

export const DictDatabaseName = 'dicts';
export const DictSchema = SchemaFactory.createForClass(DictEntity);

export type DictDocument = DictEntity & Document;
