import { BadRequestException, Body, Controller, Delete, Get, InternalServerErrorException, Param, Post, Put, Query } from "@nestjs/common";
import { AuthJwtGuard } from "src/common/auth/decorators/auth.jwt.decorator";
import { ENUM_ERROR_STATUS_CODE_ERROR } from "src/common/error/constants/error.status-code.constant";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { RequestTimezone } from "src/common/request/decorators/request.decorator";
import { Response, ResponseList, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { ENUM_GIANT_STATUS_CODE_ERROR } from "../constants/dict.status-code.constant";
import { DictCreateDto } from "../dtos/dict.create.dto";
import { DictListDto } from "../dtos/dict.list.dto";
import { DictUpdateDto } from "../dtos/dict.update.dto";
import { DictListSerializtion } from "../serializations/dict.list.serialization";
import { DictService } from "../services/dict.service";

@AuthJwtGuard()
@Controller('/dict')
export class DictController {
  constructor(
    private readonly dictService: DictService,
    private readonly paginationService: PaginationService,
  ) { }


  @ResponsePaging('dict.list', {
    classSerialization: DictListSerializtion
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      fields,
      sort,
      search,
      availableSort,
      availableSearch,
    }: DictListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = Object.assign({
      ...fields
    })

    const dicts = await this.dictService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })
    const total = await this.dictService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: dicts
    }
  }

  @ResponseList('dict.select', {
    classSerialization: DictListSerializtion
  })
  @Get('/select/:name')
  async select(@Param('name') name: string) {
    const data = await this.dictService.findAll({
      name
    })
    return {
      data
    };
  }

  @Response('dict.create')
  @Post('/create')
  async create(@Body() dictCreateDto: DictCreateDto) {
    const exists = await this.dictService.exists(dictCreateDto);
    if (exists) {
      throw new BadRequestException({
        statusCode: ENUM_GIANT_STATUS_CODE_ERROR.GIANT_EXIST_ERROR,
        message: 'dict.error.exist',
      });
    }

    try {
      const create = await this.dictService.create(dictCreateDto);
      return create;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('dict.update')
  @Put('/update/:id')
  async update(
    @Param('id') id: string,
    @Body() dictUpdateDto: DictUpdateDto,
    @RequestTimezone() timezone: string
  ) {
    try {
      await this.dictService.updateOneById(id, dictUpdateDto, timezone);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('dict.delete')
  @Delete('/delete/:id')
  async delete(
    @Param('id') id: string
  ) {
    try {
      return await this.dictService.deleteById(id);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }
}