// import { Modu<PERSON> } from "@nestjs/common";
// import { MongooseModule } from "@nestjs/mongoose";
// import { DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
// import { MailModule } from "src/common/mail/mail.module";
// import { MailBindGateway } from "./gateways/mail_bind.gateway";
// import { GiantDatabaseName, GiantEntity, GiantSchema } from "./schemas/giant.schema";
// import { InviteDatabaseName, InviteEntity, InviteSchema } from "./schemas/invite.schema";
// import { MailBindDatabaseName, MailBindEntity, MailBindSchema } from "./schemas/mail_bind.schema";
// import { MailFindEntity, MailFindSchema, MailFindDatabaseName } from "./schemas/mail_find.schema";
// import { GiantService } from "./services/giant.service";
// import { InviteService } from "./services/invite.service";
// import { MailBindService } from "./services/mail_bind.service";
// import { MailFindService } from "./services/mail_find.service";

import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { COMMUNITY_DATABASE_CONNECTION_NAME, DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { SyncService } from "./services/sync.service";
import { UserDatabaseName, UserEntity, UserSchema } from "../user/schemas/user.schema";
import { GiantDatabaseName, GiantEntity, GiantSchema } from "src/modules/giant/schemas/giant.schema";
import { ExchangeDatabaseName, ExchangeEntity, ExchangeSchema } from "./schemas/exchange.schema";
import { SummaryDatabaseName, SummaryEntity, SummarySchema } from "./schemas/summary.schema";
import { GiantService } from "src/modules/giant/services/giant.service";
import { MailFindDatabaseName, MailFindEntity, MailFindSchema } from "src/modules/giant/schemas/mail_find.schema";
import { RecordModule } from "src/modules/record/record.module";

@Module({
  imports: [
    RecordModule,
    MongooseModule.forFeature(
      [
        {
          name: UserEntity.name,
          schema: UserSchema,
          collection: UserDatabaseName
        },
        {
          name: ExchangeEntity.name,
          schema: ExchangeSchema,
          collection: ExchangeDatabaseName
        },
        {
          name: SummaryEntity.name,
          schema: SummarySchema,
          collection: SummaryDatabaseName
        },
      ],
      COMMUNITY_DATABASE_CONNECTION_NAME
    ),
    MongooseModule.forFeature(
      [
        {
          name: GiantEntity.name,
          schema: GiantSchema,
          collection: GiantDatabaseName
        },
        // for giant service
        {
          name: MailFindEntity.name,
          schema: MailFindSchema,
          collection: MailFindDatabaseName
        },
      ],
      DATABASE_CONNECTION_NAME
    ),
  ],
  exports: [SyncService],
  providers: [SyncService, GiantService],
  controllers: []
})
export class SyncModule { }