
import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { UserEntity } from '../../user/schemas/user.schema';

// import { Document, Schema, model } from "mongoose";

// SummarySchema
// export const SummarySchema = new Schema({
//   user: {
//     type: Schema.Types.ObjectId,
//     ref: 'User'
//   },
//   point: {
//     type: Number,
//     default: 0
//   },
//   signin_days: {
//     type: Number,
//     default: 0
//   },
//   topic: {
//     type: Number,
//     default: 0
//   },
//   reply: {
//     type: Number,
//     default: 0
//   },
//   collect: {
//     type: Number,
//     default: 0
//   },
//   like: {
//     type: Number,
//     default: 0
//   },
//   view: {
//     type: Number,
//     default: 0
//   },
//   solve_topic: {
//     type: Number,
//     default: 0
//   },
//   recommend_topic: {
//     type: Number,
//     default: 0
//   },
//   receive_like: {
//     type: Number,
//     default: 0
//   },
//   receive_reply: {
//     type: Number,
//     default: 0
//   },
//   receive_collect: {
//     type: Number,
//     default: 0
//   },
// }, {
//   strict: false
// })

// export const Summary = model<Summary>('Summary', SummarySchema)


@Schema({ timestamps: true, versionKey: false })
export class SummaryEntity {
  @Prop({
    ref: UserEntity.name
  })
  user: Types.ObjectId;

  @Prop({
    type: Number,
    default: 0
  })
  point: number;

  @Prop({
    type: Number,
    default: 0
  })
  signin_days: number;

  @Prop({
    type: Number,
    default: 0
  })
  topic: number;

  @Prop({
    type: Number,
    default: 0
  })
  reply: number;

  @Prop({
    type: Number,
    default: 0
  })
  collect: number;

  @Prop({
    type: Number,
    default: 0
  })
  like: number;

  @Prop({
    type: Number,
    default: 0
  })
  view: number;

  @Prop({
    type: Number,
    default: 0
  })
  solve_topic: number;

  @Prop({
    type: Number,
    default: 0
  })
  recommend_topic: number;

  @Prop({
    type: Number,
    default: 0
  })
  receive_like: number;

  @Prop({
    type: Number,
    default: 0
  })
  receive_reply: number;

  @Prop({
    type: Number,
    default: 0
  })
  receive_collect: number;
}

export const SummaryDatabaseName = 'summaries';
export const SummarySchema = SchemaFactory.createForClass(SummaryEntity);

export type SummaryDocument = SummaryEntity & Document;
