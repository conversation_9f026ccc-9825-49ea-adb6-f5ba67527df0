
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true, versionKey: false })
export class ExchangeEntity {
  @Prop({
    type: String
  })
  name: string;

  @Prop({
    type: Number
  })
  point: number;

  @Prop({
    type: Number
  })
  days: number;

  @Prop({
    type: Number
  })
  order: number;

  @Prop({
    type: Boolean,
    default: false
  })
  disabled: boolean;

  @Prop({
    type: Boolean,
    default: false
  })
  deleted: boolean;
}

export const ExchangeDatabaseName = 'exchanges';
export const ExchangeSchema = SchemaFactory.createForClass(ExchangeEntity);

export type ExchangeDocument = ExchangeEntity & Document;
