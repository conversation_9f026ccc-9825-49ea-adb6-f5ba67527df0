import { applyDecorators, createParamDecorator, ExecutionContext, UseGuards } from '@nestjs/common';
import { CommunityUserGuard } from 'src/common/auth/guards/community/auth.community-user.guard';
import { IUserDocument } from '../../user/user.interface';

// export const GetGiant = createParamDecorator(
//     (data: string, ctx: ExecutionContext): IGiantDocument => {
//         const { __giant } = ctx.switchToHttp().getRequest();
//         return data ? __giant[data] : __giant;
//     }
// );

// export const GetKey = createParamDecorator(
//     (data: string, ctx: ExecutionContext): IGiantDocument => {
//         const { __key } = ctx.switchToHttp().getRequest();
//         return __key
//     }
// );

// export const GetMachineId = createParamDecorator(
//     (data: string, ctx: ExecutionContext): IGiantDocument => {
//         const { __machineId } = ctx.switchToHttp().getRequest();
//         return __machineId
//     }
// );

export const GetCommunityUser = createParamDecorator(
    (data: string, ctx: ExecutionContext): IUserDocument => {
        const { __user } = ctx.switchToHttp().getRequest();
        return data ? __user[data] : __user;
    }
);

export function AuthCommunityUser(): any {
    return applyDecorators(
        UseGuards(CommunityUserGuard)
    );
}
