import { Injectable, InternalServerErrorException } from "@nestjs/common";
import mongoose, { Model } from "mongoose";
import { COMMUNITY_DATABASE_CONNECTION_NAME, DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { GiantDocument, GiantEntity } from "src/modules/giant/schemas/giant.schema";
import { UserDocument, UserEntity } from "../../user/schemas/user.schema";
import { SyncGenerateDto } from "../dtos/sync.generate.dto";
import { SyncQueryDto } from "../dtos/sync.query.dto";
import { ENUM_ERROR_STATUS_CODE_ERROR } from "src/common/error/constants/error.status-code.constant";
import { ExchangeDocument, ExchangeEntity } from "../schemas/exchange.schema";
import { SummaryDocument, SummaryEntity } from "../schemas/summary.schema";
import { GiantService } from "src/modules/giant/services/giant.service";

@Injectable()
export class SyncService {
  constructor(
    @DatabaseEntity(UserEntity.name, COMMUNITY_DATABASE_CONNECTION_NAME)
    private readonly userModel: Model<UserDocument>,
    @DatabaseEntity(GiantEntity.name, DATABASE_CONNECTION_NAME)
    private readonly giantModel: Model<GiantDocument>,
    @DatabaseEntity(ExchangeEntity.name, COMMUNITY_DATABASE_CONNECTION_NAME)
    private readonly exchangeModel: Model<ExchangeDocument>,
    @DatabaseEntity(SummaryEntity.name, COMMUNITY_DATABASE_CONNECTION_NAME)
    private readonly summaryModel: Model<SummaryDocument>,
    private readonly giantService: GiantService,
  ) {
  }

  async validate(queryDto: SyncQueryDto) {
    const user = await this.userModel.findOne({
      email: queryDto.email,
      password: queryDto.password
    })

    return user
  }

  async queryGiant(email: string) {
    const giant = await this.giantModel.findOne({
      mail: email
    }).lean()

    if (giant) {
      return giant
    }
    return null
  }

  async generateGiant(email: string) {
    let giant = await this.giantModel.findOne({
      mail: email
    })

    if (!giant) {
      giant = await this.giantModel.create({
        mail: email,
        days: 0
      })
    }
    return giant.toObject()
  }

  async machineRelease(email: string, id: string) {
    const giant = await this.giantModel.findOne({
      mail: email
    })

    if (giant) {
      giant.machines = giant.machines.filter(machine => machine.id !== id)
      await giant.save()
      return giant.toObject()
    }
    return null
  }

  async bindKey(email: string, key: string) {
    const giant = await this.giantModel.findOne({
      key: key
    })

    if (!giant) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: 'key not found',
      });
    }

    if (giant.mail) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: 'key already binded',
      });
    }

    giant.mail = email
    await giant.save()
    return giant.toObject()
  }

  // TODO: need lock here, but how?
  async exchangeGiant(user: UserDocument, exchangeId: string) {
    const email = user.email
    let giant = await this.giantModel.findOne({
      mail: email
    })

    if (!giant) {
      giant = await this.giantModel.create({
        mail: email,
        days: 0
      })
    }

    const exchange = await this.exchangeModel.findOne({
      _id: exchangeId,
      deleted: false
    })

    if (!exchange) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: 'exchange not found',
      });
    }

    const summary = await this.summaryModel.findOne({
      user: user._id
    })

    // can afford
    if (summary.point >= exchange.point) {
      summary.point -= exchange.point
      this.giantService.addDaysToGiant(giant, exchange.days)
      await Promise.all([
        summary.save(),
        giant.save()
      ])
    }

    return giant.toObject()
  }

}