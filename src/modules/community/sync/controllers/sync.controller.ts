import { Body, Controller, Delete, InternalServerErrorException, Param, Post } from "@nestjs/common";
import { AuthJwtGuard } from "src/common/auth/decorators/auth.jwt.decorator";
import { ENUM_ERROR_STATUS_CODE_ERROR } from "src/common/error/constants/error.status-code.constant";
import { Response } from "src/common/response/decorators/response.decorator";
import { SyncQueryDto } from "../dtos/sync.query.dto";
import { SyncService } from "../services/sync.service";
import { SyncGenerateDto } from "../dtos/sync.generate.dto";
import { AuthCommunityUser, GetCommunityUser } from "../decorators/community-user.decorator";
import { UserDocument } from "../../user/schemas/user.schema";

@AuthCommunityUser()
@Controller('/sync')
export class SyncController {
  constructor(
    private readonly syncService: SyncService,
  ) { }

  @Response('sync.query')
  @Post('/queryGiant')
  async query(@GetCommunityUser('email') email: string) {
    try {
      const res = await this.syncService.queryGiant(email)
      return res;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('sync.generate')
  @Post('/generateGiant')
  async generate(@GetCommunityUser('email') email: string) {
    try {
      const res = await this.syncService.generateGiant(email)
      return res;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('sync.machine-release')
  @Delete('/machine-release/:id')
  async machineRelease(@GetCommunityUser('email') email: string, @Param('id') id: string) {
    try {
      const res = await this.syncService.machineRelease(email, id)
      return res;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('sync.bindKey')
  @Post('/bindKey')
  async bind(@GetCommunityUser('email') email: string, @Body('key') key: string) {
    try {
      const res = await this.syncService.bindKey(email, key)
      return res;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('sync.generateKey')
  @Post('/generateKey')
  async generateKey(@GetCommunityUser('email') email: string) {
    try {
      const res = await this.syncService.generateGiant(email)
      return res;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('sync.exchange')
  @Post('/exchange/:id')
  async exchange(@GetCommunityUser() user: UserDocument, @Param('id') id: string) {
    try {
      const res = await this.syncService.exchangeGiant(user, id)
      return res;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

}