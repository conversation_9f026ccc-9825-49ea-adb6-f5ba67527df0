import { PaginationListAbstract } from "src/common/pagination/abstracts/pagination.abstract";
import { PaginationAvailableSearch, PaginationAvailableSort, PaginationFields, PaginationPage, PaginationPerPage, PaginationSearch, PaginationSort } from "src/common/pagination/decorators/pagination.decorator";
import { IPaginationSort } from "src/common/pagination/pagination.interface";
import { GIANT_DEFAULT_AVAILABLE_SEARCH, GIANT_DEFAULT_AVAILABLE_SORT, GIANT_DEFAULT_PAGE, GIANT_DEFAULT_PER_PAGE, GIANT_DEFAULT_SORT } from "../constants/permission.list.constant";

export class PermissionListDto implements PaginationListAbstract {
  @PaginationFields([
    'name',
  ])
  readonly fields: Record<string, any>

  @PaginationSearch(GIANT_DEFAULT_AVAILABLE_SEARCH)
  readonly search: Record<string, any>

  @PaginationAvailableSearch(GIANT_DEFAULT_AVAILABLE_SEARCH)
  readonly availableSearch: string[];

  @PaginationPage(GIANT_DEFAULT_PAGE)
  readonly current: number;

  @PaginationPerPage(GIANT_DEFAULT_PER_PAGE)
  readonly pageSize: number;

  @PaginationSort(GIANT_DEFAULT_SORT, GIANT_DEFAULT_AVAILABLE_SORT)
  readonly sort: IPaginationSort;

  @PaginationAvailableSort(GIANT_DEFAULT_AVAILABLE_SORT)
  readonly availableSort: string[];
}