import { BadRequestException, Body, Controller, Delete, Get, InternalServerErrorException, Param, Post, Put, Query } from "@nestjs/common";
import { AuthJwtGuard } from "src/common/auth/decorators/auth.jwt.decorator";
import { ENUM_ERROR_STATUS_CODE_ERROR } from "src/common/error/constants/error.status-code.constant";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { RequestTimezone } from "src/common/request/decorators/request.decorator";
import { Response, ResponseList, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { ENUM_GIANT_STATUS_CODE_ERROR } from "../constants/permission.status-code.constant";
import { PermissionCreateDto } from "../dtos/permission.create.dto";
import { PermissionListDto } from "../dtos/permission.list.dto";
import { PermissionUpdateDto } from "../dtos/permission.update.dto";
import { PermissionListSerializtion } from "../serializations/permission.list.serialization";
import { PermissionService } from "../services/permission.service";

@AuthJwtGuard()
@Controller('/permission')
export class PermissionController {
  constructor(
    private readonly permissionService: PermissionService,
    private readonly paginationService: PaginationService,
  ) { }


  @ResponsePaging('permission.list', {
    classSerialization: PermissionListSerializtion
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      fields,
      sort,
      search,
      availableSort,
      availableSearch,
    }: PermissionListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = Object.assign({
      ...fields
    })

    const permissions = await this.permissionService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })
    const total = await this.permissionService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: permissions
    }
  }

  @ResponseList('permission.select', {
    classSerialization: PermissionListSerializtion
  })
  @Get('/select')
  async select() {
    const data = await this.permissionService.findAll();
    return {
      data
    };
  }

  @Response('permission.create')
  @Post('/create')
  async create(@Body() permissionCreateDto: PermissionCreateDto) {
    const exists = await this.permissionService.exists(permissionCreateDto.name);
    if (exists) {
      throw new BadRequestException({
        statusCode: ENUM_GIANT_STATUS_CODE_ERROR.GIANT_EXIST_ERROR,
        message: 'permission.error.exist',
      });
    }

    try {
      const create = await this.permissionService.create(permissionCreateDto);
      return create;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('permission.update')
  @Put('/update/:id')
  async update(
    @Param('id') id: string,
    @Body() permissionUpdateDto: PermissionUpdateDto,
    @RequestTimezone() timezone: string
  ) {
    try {
      await this.permissionService.updateOneById(id, permissionUpdateDto, timezone);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('permission.delete')
  @Delete('/delete/:id')
  async delete(
    @Param('id') id: string
  ) {
    try {
      return await this.permissionService.deleteById(id);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }
}