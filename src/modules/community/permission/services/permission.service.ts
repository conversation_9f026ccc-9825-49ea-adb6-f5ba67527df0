import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { FilterQuery, Model } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { PermissionCreateDto } from "../dtos/permission.create.dto";
import { PermissionUpdateDto } from "../dtos/permission.update.dto";
import { IPermissionDocument } from "../permission.interface";
import { PermissionDocument, PermissionEntity } from "../schemas/permission.schema";
import { COMMUNITY_DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";

@Injectable()
export class PermissionService {
  constructor(
    @DatabaseEntity(PermissionEntity.name, COMMUNITY_DATABASE_CONNECTION_NAME)
    private readonly permissionModel: Model<PermissionDocument>,
  ) {
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IPermissionDocument[]> {
    const permissions = this.permissionModel.find(find)
    if (
      options &&
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      permissions.limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      permissions.sort(options.sort)
    }

    return permissions.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.permissionModel.countDocuments(find)
  }

  async exists(name: string) {
    const exists = await this.permissionModel.exists({
      name,
    });
    return !!exists;
  }

  async findOne(filterQuery: FilterQuery<PermissionDocument>) {
    const permission = await this.permissionModel.findOne(filterQuery);
    return permission;
  }

  async create(data: PermissionCreateDto) {
    const create: PermissionDocument = new this.permissionModel(data);
    return create.save();
  }

  async deleteById(id: string) {
    return this.permissionModel.findByIdAndDelete(id)
  }

  async updateOneById(_id: string, permissionUpdateDto: PermissionUpdateDto, timezone: string) {
    const update: PermissionDocument = await this.permissionModel.findById(_id);
    if (!update) {
      throw new InternalServerErrorException('Permission not found')
    }
    // update document by _id, replace by permissionUpdateDto
    update.set(permissionUpdateDto)
    return update.save()
  }

}