import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { PermissionEntity } from '../../permission/schemas/permission.schema';

@Schema({ timestamps: true, versionKey: false })
export class BoardEntity {
  @Prop({
    required: true,
    unique: true,
  })
  name: string;

  @Prop({
    required: true,
  })
  title: string;

  @Prop({
    required: true,
  })
  description: string;

  @Prop({
    required: true,
  })
  color: string;

  @Prop({
    required: true,
  })
  icon: string;

  @Prop({
    type: [{
      type: Types.ObjectId,
      ref: PermissionEntity.name,
    }],
    set: (value: (null | string | Types.ObjectId)[]) => {
      return value?.map((item) => {
        if (typeof item === 'string') {
          return new Types.ObjectId(item);
        }
        return item
      }) || []
    }
  })
  permissions: Types.ObjectId[];

  @Prop({
    type: [String]
  })
  rules: string[];

  @Prop({
    type: Number,
    default: 0,
  })
  order: number
}

export const BoardDatabaseName = 'boards';
export const BoardSchema = SchemaFactory.createForClass(BoardEntity);

export type BoardDocument = BoardEntity & Document;
