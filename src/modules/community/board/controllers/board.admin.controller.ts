import { BadRequestException, Body, Controller, Delete, Get, InternalServerErrorException, Param, Post, Put, Query } from "@nestjs/common";
import { AuthJwtGuard } from "src/common/auth/decorators/auth.jwt.decorator";
import { ENUM_ERROR_STATUS_CODE_ERROR } from "src/common/error/constants/error.status-code.constant";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { RequestTimezone } from "src/common/request/decorators/request.decorator";
import { Response, ResponseList, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { ENUM_GIANT_STATUS_CODE_ERROR } from "../constants/board.status-code.constant";
import { BoardCreateDto } from "../dtos/board.create.dto";
import { BoardListDto } from "../dtos/board.list.dto";
import { BoardUpdateDto } from "../dtos/board.update.dto";
import { BoardListSerializtion } from "../serializations/board.list.serialization";
import { BoardService } from "../services/board.service";

@AuthJwtGuard()
@Controller('/board')
export class BoardController {
  constructor(
    private readonly boardService: BoardService,
    private readonly paginationService: PaginationService,
  ) { }


  @ResponsePaging('board.list', {
    classSerialization: BoardListSerializtion
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      fields,
      sort,
      search,
      availableSort,
      availableSearch,
    }: BoardListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = Object.assign({
      ...fields
    })

    const boards = await this.boardService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })
    const total = await this.boardService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: boards
    }
  }

  @ResponseList('board.select', {
    classSerialization: BoardListSerializtion
  })
  @Get('/select')
  async select() {
    const data = await this.boardService.findAll();
    return {
      data
    };
  }

  @Response('board.create')
  @Post('/create')
  async create(@Body() boardCreateDto: BoardCreateDto) {
    try {
      const create = await this.boardService.create(boardCreateDto);
      return create;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('board.update')
  @Put('/update/:id')
  async update(
    @Param('id') id: string,
    @Body() boardUpdateDto: BoardUpdateDto,
    @RequestTimezone() timezone: string
  ) {
    try {
      await this.boardService.updateOneById(id, boardUpdateDto, timezone);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('board.delete')
  @Delete('/delete/:id')
  async delete(
    @Param('id') id: string
  ) {
    try {
      return await this.boardService.deleteById(id);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }
}