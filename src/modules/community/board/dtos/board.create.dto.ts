import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from "class-validator";

export class BoardCreateDto {
  @IsString()
  readonly name: string

  @IsString()
  readonly title: string

  @IsString()
  readonly description: string

  @IsString()
  readonly color: string

  @IsString()
  readonly icon: string

  @IsArray()
  readonly rules: string

  @IsArray()
  @IsOptional()
  readonly permissions: string

  @IsNumber()
  @IsOptional()
  readonly order: number
}