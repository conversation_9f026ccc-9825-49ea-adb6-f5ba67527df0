import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { FilterQuery, Model, Types } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { BoardCreateDto } from "../dtos/board.create.dto";
import { BoardUpdateDto } from "../dtos/board.update.dto";
import { IBoardDocument } from "../board.interface";
import { BoardDocument, BoardEntity } from "../schemas/board.schema";
import { COMMUNITY_DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";

@Injectable()
export class BoardService {
  constructor(
    @DatabaseEntity(BoardEntity.name, COMMUNITY_DATABASE_CONNECTION_NAME)
    private readonly boardModel: Model<BoardDocument>,
  ) {
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IBoardDocument[]> {
    const boards = this.boardModel.find(find)
    if (
      options &&
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      boards.limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      boards.sort(options.sort)
    }

    return boards.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.boardModel.countDocuments(find)
  }

  async findOne(filterQuery: FilterQuery<BoardDocument>) {
    const board = await this.boardModel.findOne(filterQuery);
    return board;
  }

  async create(data: BoardCreateDto) {
    const create: BoardDocument = new this.boardModel(data);
    return create.save();
  }

  async deleteById(id: string) {
    return this.boardModel.findByIdAndDelete(id)
  }

  async updateOneById(_id: string, boardUpdateDto: BoardUpdateDto, timezone: string) {
    const update: BoardDocument = await this.boardModel.findById(_id);
    if (!update) {
      throw new InternalServerErrorException('Board not found')
    }
    // update document by _id, replace by boardUpdateDto
    update.set(boardUpdateDto)
    return update.save()
  }

}