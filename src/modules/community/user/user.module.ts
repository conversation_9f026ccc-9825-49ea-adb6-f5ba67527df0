import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { COMMUNITY_DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { UserService } from "./services/user.service";
import { UserDatabaseName, UserEntity, UserSchema } from "./schemas/user.schema";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: UserEntity.name,
          schema: UserSchema,
          collection: UserDatabaseName
        },
      ],
      COMMUNITY_DATABASE_CONNECTION_NAME
    )
  ],
  exports: [UserService],
  providers: [UserService],
  controllers: []
})
export class UserModule { }