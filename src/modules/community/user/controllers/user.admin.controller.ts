import { BadRequestException, Body, Controller, Delete, Get, InternalServerErrorException, Param, Post, Put, Query } from "@nestjs/common";
import { AuthJwtGuard } from "src/common/auth/decorators/auth.jwt.decorator";
import { ENUM_ERROR_STATUS_CODE_ERROR } from "src/common/error/constants/error.status-code.constant";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { RequestTimezone } from "src/common/request/decorators/request.decorator";
import { Response, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { ENUM_GIANT_STATUS_CODE_ERROR } from "../constants/user.status-code.constant";
import { UserCreateDto } from "../dtos/user.create.dto";
import { UserListDto } from "../dtos/user.list.dto";
import { UserUpdateDto } from "../dtos/user.update.dto";
import { UserListSerializtion } from "../serializations/user.list.serialization";
import { UserService } from "../services/user.service";

@AuthJwtGuard()
@Controller('/user')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly paginationService: PaginationService,
  ) { }


  @ResponsePaging('user.list', {
    classSerialization: UserListSerializtion
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      fields,
      sort,
      search,
      availableSort,
      availableSearch,
    }: UserListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = Object.assign({
      ...fields
    })

    const users = await this.userService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })
    const total = await this.userService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: users
    }
  }

  @Response('user.create')
  @Post('/create')
  async create(@Body() userCreateDto: UserCreateDto) {
    const exists = await this.userService.exists(userCreateDto.name);
    if (exists) {
      throw new BadRequestException({
        statusCode: ENUM_GIANT_STATUS_CODE_ERROR.GIANT_EXIST_ERROR,
        message: 'user.error.exist',
      });
    }

    try {
      const create = await this.userService.create(userCreateDto);
      return create;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('user.update')
  @Put('/update/:id')
  async update(
    @Param('id') id: string,
    @Body() userUpdateDto: UserUpdateDto,
    @RequestTimezone() timezone: string
  ) {
    try {
      await this.userService.updateOneById(id, userUpdateDto, timezone);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('user.delete')
  @Delete('/delete/:id')
  async delete(
    @Param('id') id: string
  ) {
    try {
      return await this.userService.deleteById(id);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }
}