import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true, versionKey: false })
export class UserEntity {
  @Prop({
    required: true,
  })
  name: string;

  @Prop({
    default: () => 'normal',
  })
  role: string;

  // avatar
  @Prop({
    default: () => '',
  })
  avatar: string;

  // email
  @Prop({
    required: true,
  })
  email: string;

  // password
  @Prop({
    required: true,
  })
  password: string;

  // salt
  @Prop({
    required: true,
  })
  salt: string;
}

export const UserDatabaseName = 'users';
export const UserSchema = SchemaFactory.createForClass(UserEntity);

export type UserDocument = UserEntity & Document;
