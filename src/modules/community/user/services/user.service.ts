import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { FilterQuery, Model } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { UserCreateDto } from "../dtos/user.create.dto";
import { UserUpdateDto } from "../dtos/user.update.dto";
import { IUserDocument } from "../user.interface";
import { UserDocument, UserEntity } from "../schemas/user.schema";
import { COMMUNITY_DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { HelperHashService } from "src/common/helper/services/helper.hash.service";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class UserService {
  constructor(
    @DatabaseEntity(UserEntity.name, COMMUNITY_DATABASE_CONNECTION_NAME)
    private readonly userModel: Model<UserDocument>,
    private readonly helperHashService: HelperHashService,
    private readonly configService: ConfigService,
  ) {
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IUserDocument[]> {
    const users = this.userModel.find(find)
    if (
      options &&
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      users.limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      users.sort(options.sort)
    }

    return users.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.userModel.countDocuments(find)
  }

  async exists(name: string) {
    const exists = await this.userModel.exists({
      name,
    });
    return !!exists;
  }

  async findOne(filterQuery: FilterQuery<UserDocument>) {
    const user = await this.userModel.findOne(filterQuery);
    return user;
  }

  async create(data: UserCreateDto) {
    const { passwordHash, salt } = await this.createPassword(data.password)
    const create: UserDocument = new this.userModel({
      ...data,
      password: passwordHash,
      salt
    });
    return create.save();
  }

  private async createPassword(password: string) {
    const saltLength: number = this.configService.get<number>(
      'auth.password.saltLength'
    );

    const salt: string = this.helperHashService.randomSalt(saltLength);
    const passwordHash = this.helperHashService.bcrypt(password, salt);
    return {
      passwordHash,
      salt,
    };
  }

  async deleteById(id: string) {
    return this.userModel.findByIdAndDelete(id)
  }

  async updateOneById(_id: string, userUpdateDto: UserUpdateDto, timezone: string) {
    const update: UserDocument = await this.userModel.findById(_id);
    if (!update) {
      throw new InternalServerErrorException('User not found')
    }
    // update document by _id, replace by userUpdateDto
    update.set(userUpdateDto)
    return update.save()
  }

}