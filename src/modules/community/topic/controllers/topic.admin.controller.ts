import { BadRequestException, Body, Controller, Delete, Get, InternalServerErrorException, Param, Post, Put, Query } from "@nestjs/common";
import { AuthJwtGuard } from "src/common/auth/decorators/auth.jwt.decorator";
import { ENUM_ERROR_STATUS_CODE_ERROR } from "src/common/error/constants/error.status-code.constant";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { RequestTimezone } from "src/common/request/decorators/request.decorator";
import { Response, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { ENUM_GIANT_STATUS_CODE_ERROR } from "../constants/topic.status-code.constant";
import { TopicCreateDto } from "../dtos/topic.create.dto";
import { TopicListDto } from "../dtos/topic.list.dto";
import { TopicUpdateDto } from "../dtos/topic.update.dto";
import { TopicListSerializtion } from "../serializations/topic.list.serialization";
import { TopicService } from "../services/topic.service";

@AuthJwtGuard()
@Controller('/topic')
export class TopicController {
  constructor(
    private readonly topicService: TopicService,
    private readonly paginationService: PaginationService,
  ) { }


  @ResponsePaging('topic.list', {
    classSerialization: TopicListSerializtion
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      fields,
      sort,
      search,
      availableSort,
      availableSearch,
    }: TopicListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = Object.assign({
      ...fields
    })

    const topics = await this.topicService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })
    const total = await this.topicService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: topics
    }
  }

  // @Response('topic.create')
  // @Post('/create')
  // async create(@Body() topicCreateDto: TopicCreateDto) {
  //   const exists = await this.topicService.exists(topicCreateDto.name);
  //   if (exists) {
  //     throw new BadRequestException({
  //       statusCode: ENUM_GIANT_STATUS_CODE_ERROR.GIANT_EXIST_ERROR,
  //       message: 'topic.error.exist',
  //     });
  //   }

  //   try {
  //     const create = await this.topicService.create(topicCreateDto);
  //     return create;
  //   } catch (err: any) {
  //     throw new InternalServerErrorException({
  //       statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
  //       message: 'http.serverError.internalServerError',
  //       error: err.message,
  //     });
  //   }
  // }

  // @Response('topic.update')
  // @Put('/update/:id')
  // async update(
  //   @Param('id') id: string,
  //   @Body() topicUpdateDto: TopicUpdateDto,
  //   @RequestTimezone() timezone: string
  // ) {
  //   try {
  //     await this.topicService.updateOneById(id, topicUpdateDto, timezone);
  //   } catch (err: any) {
  //     throw new InternalServerErrorException({
  //       statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
  //       message: 'http.serverError.internalServerError',
  //       error: err.message,
  //     });
  //   }
  // }

  @Response('topic.hide')
  @Get('/hide/:id')
  async hide(
    @Param('id') id: string
  ) {
    try {
      return await this.topicService.hideById(id);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('topic.hide')
  @Get('/reveal/:id')
  async reveal(
    @Param('id') id: string
  ) {
    try {
      return await this.topicService.revealById(id);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('topic.delete')
  @Delete('/delete/:id')
  async delete(
    @Param('id') id: string
  ) {
    try {
      return await this.topicService.deleteById(id);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }
}