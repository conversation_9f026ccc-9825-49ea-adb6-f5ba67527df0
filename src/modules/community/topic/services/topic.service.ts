import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { FilterQuery, Model } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { TopicCreateDto } from "../dtos/topic.create.dto";
import { TopicUpdateDto } from "../dtos/topic.update.dto";
import { ITopicDocument } from "../topic.interface";
import { TopicDocument, TopicEntity } from "../schemas/topic.schema";
import { COMMUNITY_DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { HelperHashService } from "src/common/helper/services/helper.hash.service";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class TopicService {
  constructor(
    @DatabaseEntity(TopicEntity.name, COMMUNITY_DATABASE_CONNECTION_NAME)
    private readonly topicModel: Model<TopicDocument>,
    private readonly helperHashService: HelperHashService,
    private readonly configService: ConfigService,
  ) {
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<ITopicDocument[]> {
    const topics = this.topicModel.find(find)
    if (
      options &&
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      topics.populate('author').limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      topics.sort(options.sort)
    }

    return topics.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.topicModel.countDocuments(find)
  }

  async exists(name: string) {
    const exists = await this.topicModel.exists({
      name,
    });
    return !!exists;
  }

  async findOne(filterQuery: FilterQuery<TopicDocument>) {
    const topic = await this.topicModel.findOne(filterQuery);
    return topic;
  }

  async create(data: TopicCreateDto) {
    const { passwordHash, salt } = await this.createPassword(data.password)
    const create: TopicDocument = new this.topicModel({
      ...data,
      password: passwordHash,
      salt
    });
    return create.save();
  }

  private async createPassword(password: string) {
    const saltLength: number = this.configService.get<number>(
      'auth.password.saltLength'
    );

    const salt: string = this.helperHashService.randomSalt(saltLength);
    const passwordHash = this.helperHashService.bcrypt(password, salt);
    return {
      passwordHash,
      salt,
    };
  }

  async hideById(id: string) {
    return await this.topicModel.findByIdAndUpdate(id, {
      isHidden: true
    })
  }

  async revealById(id: string) {
    return await this.topicModel.findByIdAndUpdate(id, {
      isHidden: false
    })
  }

  async deleteById(id: string) {
    return this.topicModel.findByIdAndDelete(id)
  }

  async updateOneById(_id: string, topicUpdateDto: TopicUpdateDto, timezone: string) {
    const update: TopicDocument = await this.topicModel.findById(_id);
    if (!update) {
      throw new InternalServerErrorException('Topic not found')
    }
    // update document by _id, replace by topicUpdateDto
    update.set(topicUpdateDto)
    return update.save()
  }

}