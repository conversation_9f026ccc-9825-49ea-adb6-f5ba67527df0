import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { UserEntity } from '../../user/schemas/user.schema';

@Schema({ timestamps: true, versionKey: false })
export class TopicEntity {
  @Prop({
    required: true,
  })
  title: string;

  @Prop({
    default: () => 'normal',
  })
  category: string;

  // author
  @Prop({
      required: true,
      type: Types.ObjectId,
      ref: UserEntity.name,
  })
  author: Types.ObjectId;

  // email
  @Prop({
    required: true,
  })
  lastReply: Date;

  // password
  @Prop({
    required: true,
  })
  sticky: number;

  // viewCount
  @Prop({
    required: true,
  })
  viewCount: number;

  // collectCount
  @Prop({
    required: true,
  })
  collectCount: number;

  // likeCount
  @Prop({
    required: true,
  })
  likeCount: number;

  // replyCount
  @Prop({
    required: true,
  })
  replyCount: number;

  // isHidden
  @Prop({
    required: true,
  })
  isHidden: boolean;
}

export const TopicDatabaseName = 'topics';
export const TopicSchema = SchemaFactory.createForClass(TopicEntity);

export type TopicDocument = TopicEntity & Document;
