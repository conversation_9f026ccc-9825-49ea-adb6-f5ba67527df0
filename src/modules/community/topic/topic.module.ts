import { Mo<PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { COMMUNITY_DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { TopicService } from "./services/topic.service";
import { TopicDatabaseName, TopicEntity, TopicSchema } from "./schemas/topic.schema";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: TopicEntity.name,
          schema: TopicSchema,
          collection: TopicDatabaseName
        },
      ],
      COMMUNITY_DATABASE_CONNECTION_NAME
    )
  ],
  exports: [TopicService],
  providers: [TopicService],
  controllers: []
})
export class TopicModule { }