import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true, versionKey: false })
export class VersionEntity {
  @Prop({
    required: true,
    type: Number,
  })
  version: number;

  @Prop({
    required: true,
    type: String
  })
  content: string;
}

export const VersionDatabaseName = 'versions';
export const VersionSchema = SchemaFactory.createForClass(VersionEntity);

export type VersionDocument = VersionEntity & Document;
