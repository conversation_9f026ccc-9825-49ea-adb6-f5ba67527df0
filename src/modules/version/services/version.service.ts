import { Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { IVersionDocument } from "../version.interface";
import { VersionDocument, VersionEntity } from "../schemas/version.schema";
import MarkdownIt from "markdown-it";

@Injectable()
export class VersionService {
  constructor(
    @DatabaseEntity(VersionEntity.name)
    private readonly versionModel: Model<VersionDocument>
  ) {
  }

  async findVersionsGreaterThan(version: string) {
    console.log(this.parseVersion(version))
    return await this.versionModel.find({
      version: {
        $gt: this.parseVersion(version)
      }
    })
  }

  async findByVersion(version: string) {
    return await this.versionModel.findOne({
      version: this.parseVersion(version)
    })
  }

  parseVersion(ver: string) {
    if (typeof ver != 'string') {
      return 0
    }
    const [major, minor, patch] = ver.split('.').map(v => parseInt(v))
    if (major < 256 && minor < 256 && patch < 256) {
      return (major << 16) + (minor << 8) + patch
    }
    return 0
  }

  toHtml(mdText: string) {
    const markdown = MarkdownIt({
      html: true
    })

    const rendered = markdown.render(mdText)
    return `<!DOCTYPE html>
    <html lang="en">
    <head>
    <meta charset="UTF-8" />
    <title>version</title>
    <link rel="stylesheet" href="./default.css">
    </head>
    <body>
    ${rendered}
    </body>
    </html>`;
  }

}