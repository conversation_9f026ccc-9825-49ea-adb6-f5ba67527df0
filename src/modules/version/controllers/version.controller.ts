import { <PERSON>, Get, Param, Post, Query } from "@nestjs/common";
import { VersionService } from "../services/version.service";

@Controller('/version')
export class VersionController {
  constructor(
    private readonly versionService: VersionService,
  ) { }

  @Get()
  async getVersion(
    @Query('v') version: string,
    @Query('s') greater: number
  ) {
    let content = ''
    if (greater) {
      const versions = await this.versionService.findVersionsGreaterThan(version)
      content = versions.reverse().map(v => v.content).join('\n')
    } else {
      const currentVersion = await this.versionService.findByVersion(version)
      content = currentVersion?.content || ''
    }
    return this.versionService.toHtml(content)
  }
}