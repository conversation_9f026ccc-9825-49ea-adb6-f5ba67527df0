import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { VersionDatabaseName, VersionEntity, VersionSchema } from "./schemas/version.schema";
import { VersionService } from "./services/version.service";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: VersionEntity.name,
          schema: VersionSchema,
          collection: VersionDatabaseName
        }
      ],
      DATABASE_CONNECTION_NAME
    )
  ],
  exports: [VersionService],
  providers: [VersionService],
  controllers: []
})
export class VersionModule { }