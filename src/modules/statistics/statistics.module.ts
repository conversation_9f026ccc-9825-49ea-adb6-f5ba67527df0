import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { SettingDatabaseName, SettingSchema } from "src/common/setting/schemas/setting.schema";
import { StatisticsEntity } from "./schemas/statistics.schema";
import { StatisticsService } from "./services/statistics.service";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: StatisticsEntity.name,
          schema: SettingSchema,
          collection: SettingDatabaseName
        }
      ],
      DATABASE_CONNECTION_NAME
    )
  ],
  exports: [StatisticsService],
  providers: [StatisticsService]
})
export class StatisticsModule { }