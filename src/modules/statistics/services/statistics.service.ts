import { Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { StatisticsDocument, StatisticsEntity } from "../schemas/statistics.schema";

@Injectable()
export class StatisticsService {
  constructor(
    @DatabaseEntity(StatisticsEntity.name)
    private readonly statisticsModel: Model<StatisticsDocument>,
  ) { }

  async findOneByName(name: string): Promise<StatisticsDocument> {
    return this.statisticsModel.findOne({ name }).lean()
  }

  async getDownloadCount() {
    const doc = await this.statisticsModel.findOne({
      name: 'downloadCount'
    })

    return doc && doc.value || 0
  }

  async incrementDownloadCount() {
    let doc = await this.statisticsModel.findOne({
      name: 'downloadCount'
    })

    if (doc) {
      doc.value = (doc.value as number) + 1
    } else {
      doc = new this.statisticsModel({
        name: 'downloadCount',
        value: 0,
        description: 'gross download count'
      })
    }
    await doc.save()
  }

}