import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true, versionKey: false })
export class StatisticsEntity {
  @Prop({
    required: true,
    index: true,
    unique: true,
    trim: true,
  })
  name: string;

  @Prop({
    required: false,
  })
  description?: string;

  @Prop({
    required: true,
    trim: true,
    type: MongooseSchema.Types.Mixed,
  })
  value: string | number | boolean;
}

export const StatisticsDatabaseName = 'statistics';
export const StatisticsSchema = SchemaFactory.createForClass(StatisticsEntity);

export type StatisticsDocument = StatisticsEntity & Document;
