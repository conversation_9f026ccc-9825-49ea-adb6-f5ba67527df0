import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { StatisticsDocument } from '../schemas/statistics.schema';
import { StatisticsService } from '../services/statistics.service';

@Injectable()
export class StatisticsPutToRequestByNameGuard implements CanActivate {
    constructor(private readonly statisticsService: StatisticsService) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const { params } = request;
        const { statisticsName } = params;

        const check: StatisticsDocument = await this.statisticsService.findOneByName(
            statisticsName
        );
        request.__statistics = check;

        return true;
    }
}
