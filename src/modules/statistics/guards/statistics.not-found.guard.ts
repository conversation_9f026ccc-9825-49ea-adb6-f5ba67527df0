import {
    Injectable,
    CanActivate,
    ExecutionContext,
    NotFoundException,
} from '@nestjs/common';
import { ENUM_STATISTICS_STATUS_CODE_ERROR } from '../constants/statistics.status-code.constant';

@Injectable()
export class StatisticsNotFoundGuard implements CanActivate {
    async canActivate(context: ExecutionContext): Promise<boolean> {
        const { __statistics } = context.switchToHttp().getRequest();

        if (!__statistics) {
            throw new NotFoundException({
                statusCode:
                    ENUM_STATISTICS_STATUS_CODE_ERROR.STATISTICS_NOT_FOUND_ERROR,
                message: 'statistics.error.notFound',
            });
        }

        return true;
    }
}
