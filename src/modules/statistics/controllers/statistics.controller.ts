
import { Controller, Get } from '@nestjs/common';
import {
  Response
} from 'src/common/response/decorators/response.decorator';
import {
  IResponse
} from 'src/common/response/response.interface';
import { GetStatistics } from '../decorators/statistics.decorator';
import {
  StatisticsGetByNameGuard
} from '../decorators/statistics.public.decorator';
import { StatisticsDocument } from '../schemas/statistics.schema';
import { StatisticsGetSerialization } from '../serializations/statistics.get.serialization';

@Controller({
  version: '1',
  path: '/statistics',
})
export class StatisticsController {
  @Response('statistics.getByName', {
    classSerialization: StatisticsGetSerialization,
  })
  @StatisticsGetByNameGuard()
  @Get('get/name/:statisticsName')
  async getByName(
    @GetStatistics() statistics: StatisticsDocument
  ): Promise<IResponse> {
    return statistics;
  }
}
