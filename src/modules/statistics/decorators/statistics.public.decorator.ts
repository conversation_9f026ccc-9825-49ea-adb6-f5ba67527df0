import { applyDecorators, UseGuards } from '@nestjs/common';
import { StatisticsNotFoundGuard } from '../guards/statistics.not-found.guard';
import {
    StatisticsPutToRequestByNameGuard,
} from '../guards/statistics.put-to-request.guard';

export function StatisticsGetByNameGuard(): any {
    return applyDecorators(
        UseGuards(StatisticsPutToRequestByNameGuard, StatisticsNotFoundGuard)
    );
}
