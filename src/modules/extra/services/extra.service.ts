import { Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { ExtraDocument, ExtraEntity } from "../schemas/extra.schema";

@Injectable()
export class ExtraService {

  constructor(
    @DatabaseEntity(ExtraEntity.name)
    private readonly extraModel: Model<ExtraDocument>
  ) {
  }

  async getDownloadCount () {
    return this.extraModel.findOne({
      key: 'downloadCount'
    })
  }
  
  async getBackupTime () {
    return this.extraModel.findOne({
      key: 'backupTime'
    })
  }
}