import { Controller, Get } from "@nestjs/common";
import { IExtraDocument } from "../extra.interface";
import { ExtraService } from "../services/extra.service";

@Controller('/extra')
export class ExtraController {
  constructor(
    private readonly extraService: ExtraService
  ) {}

  @Get('/getDownloadCount')
  async getDownloadCount () {
    const extra: IExtraDocument = await this.extraService.getDownloadCount()
    return extra.value
  }

  @Get('/getBackupTime')
  async getBackupTime () {
    const extra: IExtraDocument = await this.extraService.getBackupTime()
    return extra.value
  }
}