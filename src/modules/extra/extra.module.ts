import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ExtraDatabaseName, ExtraEntity, ExtraSchema } from "./schemas/extra.schema";
import { DATABASE_CONNECTION_NAME } from 'src/common/database/constants/database.constant';
import { ExtraService } from "./services/extra.service";


@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: ExtraEntity.name,
          schema: ExtraSchema,
          collection: ExtraDatabaseName
        }
      ],
      DATABASE_CONNECTION_NAME
    )
  ],
  exports: [ExtraService],
  providers: [ExtraService],
  controllers: [],
})
export class ExtraModule {}