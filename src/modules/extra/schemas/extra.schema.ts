import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true, versionKey: false })
export class ExtraEntity {
  @Prop({
    required: true,
    trim: true,
  })
  key: string;

  @Prop({
    required: true,
    type: MongooseSchema.Types.Mixed
  })
  value: string | number;
}

export const ExtraDatabaseName = 'extras';
export const ExtraSchema = SchemaFactory.createForClass(ExtraEntity);

export type ExtraDocument = ExtraEntity & Document;
