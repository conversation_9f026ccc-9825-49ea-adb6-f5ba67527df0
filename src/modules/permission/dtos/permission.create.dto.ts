import { Is<PERSON><PERSON>, <PERSON><PERSON>otEmpty, <PERSON><PERSON>te<PERSON><PERSON>, IsBoolean } from 'class-validator';

export class PermissionCreateDto {
    @IsString()
    @IsNotEmpty()
    readonly name: string;

    @IsString()
    @IsNotEmpty()
    readonly code: string;

    @IsString()
    @IsNotEmpty()
    readonly description: string;

    @IsBoolean()
    @IsNotEmpty()
    @ValidateIf((e) => e.isActive !== '')
    readonly isActive?: boolean;
}
