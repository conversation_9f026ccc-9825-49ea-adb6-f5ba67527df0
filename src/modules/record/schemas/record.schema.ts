import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true, versionKey: false })
export class RecordEntity {
  @Prop({
    index: true,
    type: String
  })
  key: string;

  // machineId
  @Prop({
    index: true,
    type: String
  })
  machineId: string;

  @Prop({
    type: String
  })
  version: string;

  // platform
  @Prop({
    type: String
  })
  platform: string;

  // ip
  @Prop({
    type: String
  })
  ip: string;

  // type: 'open' | 'close' | 'download' | 'subscribe'
  @Prop({
    type: String,
    // enum: ['open', 'close', 'download', 'subscribe', 'giant', 'trial_risk']
  })
  type: string;

  // dl_url, optional
  @Prop({
    type: String
  })
  dl_url: string;

  // dl_operateFrom, optional
  @Prop({
    type: String
  })
  dl_operateFrom: string;

  // dl_taskflow, optional
  @Prop({
    type: String
  })
  dl_taskflow: string;

  // dl_status, ['added', 'failed', 'succeed']
  @Prop({
    type: String,
    enum: ['added', 'failed', 'succeed']
  })
  dl_status: string;

  @Prop({
    type: String
  })
  dl_error: string;

  @Prop({
    type: String
  })
  dl_size: string;

  @Prop({
    type: Number
  })
  giant_days: number;

  @Prop({
    type: Boolean
  })
  new_giant: boolean;

  // sub_price, optional
  @Prop({
    type: Number
  })
  sub_price: number;

  @Prop({
    type: String
  })
  payload: string;
}

export const RecordDatabaseName = 'records';
export const RecordSchema = SchemaFactory.createForClass(RecordEntity);

export type RecordDocument = RecordEntity & Document;

