import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true, versionKey: false })
export class BlackmachineEntity {
  // machineId
  @Prop({
    index: true,
    type: String
  })
  machineId: string;
}

export const BlackmachineDatabaseName = 'blackmachines';
export const BlackmachineSchema = SchemaFactory.createForClass(BlackmachineEntity);

export type BlackmachineDocument = BlackmachineEntity & Document;

