import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true, versionKey: false })
export class KBERecordEntity {
  @Prop({
    index: true,
    type: String
  })
  key: string;

  @Prop({
    index: true,
    type: String
  })
  machineId: string;

  @Prop({
    type: String
  })
  version: string;

  // ip
  @Prop({
    type: String
  })
  ip: string;

  @Prop({
    type: String,
    enum: ['install']
  })
  type: string;

  @Prop({
    type: String
  })
  userAgent: string

  @Prop({
    type: String
  })
  platform: string
}

export const KBERecordDatabaseName = 'kberecords';
export const KBERecordSchema = SchemaFactory.createForClass(KBERecordEntity);

export type KBERecordDocument = KBERecordEntity & Document;
