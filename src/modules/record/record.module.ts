import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { MailModule } from "src/common/mail/mail.module";
import { RecordDatabaseName, RecordEntity, RecordSchema } from "./schemas/record.schema";
import { RecordService } from "./services/record.service";
import { KBERecordDatabaseName, KBERecordEntity, KBERecordSchema } from "./schemas/kberecord.schema";
import { BlackmachineDatabaseName, BlackmachineEntity, BlackmachineSchema } from "./schemas/blackmachine.schema";

@Module({
  imports: [
    MailModule,
    MongooseModule.forFeature(
      [
        {
          name: RecordEntity.name,
          schema: RecordSchema,
          collection: RecordDatabaseName
        },
        {
          name: KBERecordEntity.name,
          schema: KBERecordSchema,
          collection: KBERecordDatabaseName
        },
        {
          name: BlackmachineEntity.name,
          schema: BlackmachineSchema,
          collection: BlackmachineDatabaseName
        }
      ],
      DATABASE_CONNECTION_NAME
    )
  ],
  exports: [RecordService],
  providers: [RecordService],
  controllers: []
})
export class RecordModule { }