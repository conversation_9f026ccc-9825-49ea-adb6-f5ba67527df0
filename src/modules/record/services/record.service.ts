import { Injectable } from "@nestjs/common";
import { FilterQ<PERSON>y, Model } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { RecordCreateDto } from "../dtos/record.create.dto";
import { IRecordDocument } from "../record.interface";
import { RecordDocument, RecordEntity } from "../schemas/record.schema";
import { KBERecordCreateDto } from "../dtos/kberecord.create.dto";
import { KBERecordDocument, KBERecordEntity } from "../schemas/kberecord.schema";
import { BlackmachineEntity } from "../schemas/blackmachine.schema";

@Injectable()
export class RecordService {
  constructor(
    @DatabaseEntity(RecordEntity.name)
    private readonly recordModel: Model<RecordDocument>,
    @DatabaseEntity(KBERecordEntity.name)
    private readonly kberecordModel: Model<KBERecordDocument>,
    @DatabaseEntity(BlackmachineEntity.name)
    private readonly blackMachineModel: Model<RecordDocument>,
  ) {
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IRecordDocument[]> {
    const records = this.recordModel.find(find)
    if (
      options &&
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      records.limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      records.sort(options.sort)
    }

    return records.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.recordModel.countDocuments(find)
  }

  async findByKey(key: string) {
    const record = await this.recordModel.findOne({
      key,
    });
    return record;
  }

  async findOne(filterQuery: FilterQuery<RecordDocument>) {
    const record = await this.recordModel.findOne(filterQuery);
    return record;
  }

  async exists(key: string) {
    const exists = await this.recordModel.exists({
      key,
    });
    return !!exists;
  }

  async create(data: RecordCreateDto, ip?: string) {
    const create: RecordDocument = new this.recordModel(data);
    create.ip = ip || '';
    return create.save();
  }

  async verifyRecord(machineId: string) {
    // find if has a record which dl_error equals _error_not_giant_cap or giant_hit_exceed in the last 24 hours
    // const record = await this.recordModel.findOne({
    //   machineId,
    //   dl_error: {
    //     $in: ['_error_not_giant_cap', 'giant_hit_exceed'],
    //   },
    //   createdAt: {
    //     $gte: new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
    //   },
    // }).sort({ createdAt: 1 });

    // if (!record) return true

    // return ((record as any).createdAt).getTime() < new Date().getTime() - 24 * 60 * 60 * 1000
  }

  async verifyForce(machineId: string) {
    const m = await this.blackMachineModel.findOne({
      machineId
    })

    return !m
  }

  async createGiantRecord(data: { key: string, type: string, giant_days: number, new_giant: boolean }) {
    const create: RecordDocument = new this.recordModel(data);
    return create.save();
  }

  async createKBERecord(data: KBERecordCreateDto, ip?: string) {
    const create: KBERecordDocument = new this.kberecordModel(data);
    create.ip = ip || '';
    return create.save();
  }
}