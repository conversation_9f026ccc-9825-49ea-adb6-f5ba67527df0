import { Body, Controller, Get, InternalServerErrorException, Post } from "@nestjs/common";
import { ENUM_ERROR_STATUS_CODE_ERROR } from "src/common/error/constants/error.status-code.constant";
import { Response } from "src/common/response/decorators/response.decorator";
import { RecordCreateDto } from "../dtos/record.create.dto";
import { RecordService } from "../services/record.service";
import { RequestIp } from "src/common/request/decorators/request.decorator";
import { KBERecordCreateDto } from "../dtos/kberecord.create.dto";

@Controller('/record')
export class RecordController {
  constructor(
    private readonly recordService: RecordService,
  ) { }

  @Response('record.create')
  @Post('/create')
  async createRecord(
    @Body() recordCreateDto: RecordCreateDto,
    @RequestIp() ip: string
  ) {
    try {
      const create = this.recordService.create(recordCreateDto, ip)
      return create;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('record.verify')
  @Post('/verify')
  async verify(
    @Body('machineId') machineId: string,
    @RequestIp() ip: string
  ) {
    try {
      const res = this.recordService.verifyRecord(machineId)
      return {
        pass: res
      };
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('kberecord.create')
  @Post('/createkbe')
  async createKBERecord(
    @Body() recordCreateDto: KBERecordCreateDto,
    @RequestIp() ip: string
  ) {
    try {
      const create = this.recordService.createKBERecord(recordCreateDto, ip)
      return create;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

} 