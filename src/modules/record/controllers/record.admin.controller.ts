import { Controller, Get, Query } from "@nestjs/common";
import { AuthJwtGuard } from "src/common/auth/decorators/auth.jwt.decorator";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { RecordListDto } from "../dtos/record.list.dto";
import { RecordListSerializtion } from "../serializations/record.list.serialization";
import { RecordService } from "../services/record.service";

@AuthJwtGuard()
@Controller('/record')
export class RecordController {
  constructor(
    private readonly recordService: RecordService,
    private readonly paginationService: PaginationService,
  ) { }


  @ResponsePaging('record.list', {
    classSerialization: RecordListSerializtion
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      fields,
      sort,
      search,
      availableSort,
      availableSearch,
    }: RecordListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = Object.assign({
      ...fields
    })

    const records = await this.recordService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })
    const total = await this.recordService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: records
    }
  }

}