import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { IRecordDocument } from '../record.interface';

export const GetKey = createParamDecorator(
    (data: string, ctx: ExecutionContext): IRecordDocument => {
        const { __key } = ctx.switchToHttp().getRequest();
        return __key
    }
);

export const GetMachineId = createParamDecorator(
    (data: string, ctx: ExecutionContext): IRecordDocument => {
        const { __machineId } = ctx.switchToHttp().getRequest();
        return __machineId
    }
);
