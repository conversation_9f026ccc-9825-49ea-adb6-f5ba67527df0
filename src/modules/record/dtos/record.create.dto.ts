import { IsBoolean, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON>ptional, IsString } from "class-validator";

export class RecordCreateDto {
  @IsString()
  @IsOptional()
  readonly key: string

  @IsString()
  @IsOptional()
  readonly machineId: string

  @IsString()
  @IsOptional()
  readonly platform: string

  @IsString()
  @IsOptional()
  readonly ip: string

  @IsString()
  @IsOptional()
  readonly type: string

  @IsString()
  @IsOptional()
  readonly dl_url: string

  @IsString()
  @IsOptional()
  readonly dl_operateFrom: string

  @IsString()
  @IsOptional()
  readonly dl_taskflow: string

  @IsString()
  @IsOptional()
  readonly dl_status: string

  @IsString()
  @IsOptional()
  readonly dl_error: string

  @IsNumber()
  @IsOptional()
  readonly giant_days: number

  @IsBoolean()
  @IsOptional()
  readonly new_giant: number

  @IsString()
  @IsOptional()
  readonly sub_price: number

  @IsString()
  @IsOptional()
  readonly payload: string
}
