import { PaginationListAbstract } from "src/common/pagination/abstracts/pagination.abstract";
import { PaginationAvailableSearch, PaginationAvailableSort, PaginationFields, PaginationPage, PaginationPerPage, PaginationSearch, PaginationSort, toBoolean, toDateRange } from "src/common/pagination/decorators/pagination.decorator";
import { IPaginationSort } from "src/common/pagination/pagination.interface";
import { TimeRange } from "src/common/timerange/decorators/timerange.decorator";
import { RECORD_DEFAULT_AVAILABLE_SEARCH, RECORD_DEFAULT_AVAILABLE_SORT, RECORD_DEFAULT_PAGE, RECORD_DEFAULT_PER_PAGE, RECORD_DEFAULT_SORT } from "../constants/record.list.constant";

export class RecordListDto implements PaginationListAbstract {
  @PaginationFields([
    'key',
    {
      key: 'offline',
      transform: toBoolean
    },
    {
      key: 'activeTime',
      transform: toDateRange
    }
  ])
  readonly fields: Record<string, any>

  @PaginationSearch(RECORD_DEFAULT_AVAILABLE_SEARCH)
  readonly search: Record<string, any>

  @PaginationAvailableSearch(RECORD_DEFAULT_AVAILABLE_SEARCH)
  readonly availableSearch: string[];

  @PaginationPage(RECORD_DEFAULT_PAGE)
  readonly current: number;

  @PaginationPerPage(RECORD_DEFAULT_PER_PAGE)
  readonly pageSize: number;

  @PaginationSort(RECORD_DEFAULT_SORT, RECORD_DEFAULT_AVAILABLE_SORT)
  readonly sort: IPaginationSort;

  @PaginationAvailableSort(RECORD_DEFAULT_AVAILABLE_SORT)
  readonly availableSort: string[];
}