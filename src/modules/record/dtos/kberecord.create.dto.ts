import { <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsString } from "class-validator";

export class KBERecordCreateDto {
  @IsString()
  @IsOptional()
  readonly key: string

  @IsString()
  @IsOptional()
  readonly machineId: string

  @IsString()
  @IsOptional()
  readonly ip: string

  @IsString()
  @IsOptional()
  readonly type: string

  @IsString()
  @IsOptional()
  readonly userAgent: string

  @IsString()
  @IsOptional()
  readonly version: string

  @IsString()
  @IsOptional()
  readonly platform: string
}
