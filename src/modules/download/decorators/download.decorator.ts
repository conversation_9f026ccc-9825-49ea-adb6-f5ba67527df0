// import { applyDecorators, createParamDecorator, ExecutionContext, UseGuards } from '@nestjs/common';
// import { GiantGuard, GiantMachineExceedGuard, GiantOptionalGuard } from 'src/common/auth/guards/giant/auth.giant.guard';

// export const GetGiant = createParamDecorator(
//     (data: string, ctx: ExecutionContext): IGiantDocument => {
//         const { __giant } = ctx.switchToHttp().getRequest();
//         return data ? __giant[data] : __giant;
//     }
// );

// export const GetKey = createParamDecorator(
//     (data: string, ctx: ExecutionContext): IGiantDocument => {
//         const { __key } = ctx.switchToHttp().getRequest();
//         return __key
//     }
// );

// export const GetMachineId = createParamDecorator(
//     (data: string, ctx: ExecutionContext): IGiantDocument => {
//         const { __machineId } = ctx.switchToHttp().getRequest();
//         return __machineId
//     }
// );

// export function AuthGiant(): any {
//     return applyDecorators(
//         UseGuards(GiantGuard, GiantMachineExceedGuard)
//     );
// }

// export function OptionalGiant(): any {
//     return applyDecorators(
//         UseGuards(GiantOptionalGuard)
//     );
// }
