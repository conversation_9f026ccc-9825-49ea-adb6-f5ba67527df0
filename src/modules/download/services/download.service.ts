import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { readdir } from 'fs/promises';
import { Model } from 'mongoose';
import { join } from 'path';
import { DatabaseEntity } from 'src/common/database/decorators/database.decorator';
import {
  SettingDocument,
  SettingEntity,
} from 'src/common/setting/schemas/setting.schema';
import {
  ExtensionDocument,
  ExtensionEntity,
} from 'src/modules/extension/schemas/extension.schema';
import { createWriteStream, mkdirpSync } from 'fs-extra';
import { IFile } from 'src/common/file/file.interface';
import JSZip from "jszip";
import { StatisticsService } from 'src/modules/statistics/services/statistics.service';
import { PayAdminService } from 'src/gateways/services/pay-admin.service';
import { DownloadDocument, DownloadEntity } from '../schemas/download.schema';

@Injectable()
export class DownloadService {
  private readonly staticPrefix: string;
  private readonly staticPath: string;
  private readonly basePath: string;
  private readonly appPath: string;
  private readonly appReadyPath: string;
  private readonly appMapPath: string;
  private readonly extensionPath: string;
  private readonly browserExtensionPath: string;
  private readonly browserExtensionCompatPath: string;
  private readonly versionPath: string;
  private readonly browserExtensionVersionPath: string;
  private readonly appHomePath: string;
  private readonly downloadDomain: string;

  private readonly downloadBasePath: string;

  private lastDownloadTimes: { [key: string]: number } = {}

  constructor(
    @DatabaseEntity(SettingEntity.name)
    private readonly settingModel: Model<SettingDocument>,
    @DatabaseEntity(ExtensionEntity.name)
    private readonly extensionModel: Model<ExtensionDocument>,
    @DatabaseEntity(DownloadEntity.name)
    private readonly downloadModel: Model<DownloadDocument>,

    private readonly statisticsService: StatisticsService,
    private readonly configService: ConfigService,
    private readonly payAdminService: PayAdminService,
  ) {
    this.staticPrefix = this.configService.get<string>('download.staticPrefix');
    this.staticPath = this.configService.get<string>('download.staticPath');
    this.basePath = this.configService.get<string>('download.basePath');
    this.appPath = this.configService.get<string>('download.appPath');
    this.appReadyPath = this.configService.get<string>('download.appReadyPath');
    this.appMapPath = this.configService.get<string>('download.appMapPath');
    this.extensionPath = this.configService.get<string>(
      'download.extensionPath'
    );
    this.browserExtensionPath = this.configService.get<string>(
      'download.browserExtensionPath'
    );
    this.browserExtensionCompatPath = this.configService.get<string>(
      'download.browserExtensionCompatPath'
    );
    this.versionPath = this.configService.get<string>('download.versionPath');
    this.browserExtensionVersionPath = this.configService.get<string>(
      'download.browserExtensionVersionPath'
    );
    this.appHomePath = this.configService.get<string>('download.appHomePath');
    this.downloadDomain = this.configService.get<string>('download.domain');

    this.downloadBasePath = join(this.staticPath, this.basePath);

    this.ensurePath(this.downloadAppPath);
    this.ensurePath(this.downloadAppReadyPath);
    this.ensurePath(this.downloadAppMapPath);
    this.ensurePath(this.downloadBrowserExtensionPath);
    this.ensurePath(this.downloadBrowserExtensionCompatPath);
    this.ensurePath(this.downloadAppVersionPath);
    this.ensurePath(this.downloadBrowserExtensionVersionPath);
    this.ensurePath(this.downloadAppHomePath);
    this.ensurePath(this.downloadExtensionPath);

    setInterval(() => this.cleanupDownloadTimes(), 1000 * 60 * 60);
  }

  private cleanupDownloadTimes() {
    const now = Date.now();
    // 10 minutes
    const expirationTime = 1000 * 60 * 10;

    for (const id in this.lastDownloadTimes) {
      if (now - this.lastDownloadTimes[id] > expirationTime) {
        delete this.lastDownloadTimes[id];
      }
    }
  }

  private ensurePath(path: string) {
    mkdirpSync(path);
  }

  get downloadAppPath() {
    return join(this.downloadBasePath, this.appPath);
  }

  get downloadAppReadyPath() {
    return join(this.downloadBasePath, this.appReadyPath);
  }

  get downloadAppMapPath() {
    return join(this.downloadBasePath, this.appMapPath);
  }

  get downloadBrowserExtensionPath() {
    return join(this.downloadBasePath, this.browserExtensionPath);
  }

  get downloadBrowserExtensionCompatPath() {
    return join(this.downloadBasePath, this.browserExtensionCompatPath);
  }

  get downloadAppVersionPath() {
    return join(this.downloadBasePath, this.versionPath);
  }

  get downloadBrowserExtensionVersionPath() {
    return join(this.downloadBasePath, this.browserExtensionVersionPath);
  }

  get downloadAppHomePath() {
    return join(this.downloadBasePath, this.appHomePath);
  }

  get downloadExtensionPath() {
    return join(this.downloadBasePath, this.extensionPath);
  }

  writeFilesToPath(files: IFile[], path: string) {
    files.forEach((file) => {
      const ws = createWriteStream(join(path, file.originalname), {
        encoding: 'binary',
      });
      ws.write(file.buffer);
      ws.close();
    });
  }

  async getAppCurrentRelease() {
    const res: any = await this.settingModel.findOne({
      name: 'currentAppVersion',
    });

    let force = false
    let version = ''

    if (res && res.value) {
      version = (res.value as string).split(',')[0]
      force = (res.value as string).split(',')[1] === '1'
    }

    return {
      version,
      releaseDate: res.updatedAt ?? '',
      force
    }
  }

  async downloadApp(platform: 'win32' | 'darwin' = 'win32', arch: 'x64' | 'arm64' = 'x64', source = '', ip = '') {
    const releaseInfo = await this.getAppCurrentRelease();
    if (!releaseInfo.version) {
      return;
    }

    const files = await readdir(join(this.downloadBasePath, this.appPath));
    const file = files.find(
      (f) => {
        let pathFind = false
        if (platform === 'win32') {
          pathFind = f.endsWith('.exe')
        } else if (platform === 'darwin') {
          if (arch === 'arm64') {
            pathFind = f.endsWith('arm64.dmg')
          } else {
            pathFind = f.endsWith('.dmg') && !f.endsWith('arm64.dmg')
          }
        }
        return pathFind && (f.includes(` ` + releaseInfo.version) || f.includes(`-` + releaseInfo.version))
      }
    );

    if (!file) return;

    if (source !== 'update') {
      this.recordDownload(platform === 'win32' ? 'app-win32' : 'app-mac', `${releaseInfo.version}`, source, ip);
    }

    return join(this.staticPrefix, this.basePath, this.appPath, file)
  }

  private async recordDownload(name = 'app', version = '', source = '', ip = '') {
    const now = Date.now();
    const id = `${name}-${source}-${ip}`;
    if (this.lastDownloadTimes[id] && now - this.lastDownloadTimes[id] < 1000 * 60 * 10) {
      return;
    }

    this.lastDownloadTimes[id] = now;

    const download = new this.downloadModel({
      name,
      version,
      source,
      ip
    });

    await download.save();

    if (name.startsWith('app')) {
      await this.statisticsService.incrementDownloadCount();
      const dlcnt = await this.statisticsService.getDownloadCount();
      const cl = this.payAdminService.getClient();
      cl?.send(JSON.stringify({
        event: 'dlcnt',
        data: dlcnt
      }));
    }
  }

  async downloadAppVersionJs(version) {
    if (version === 'latest') {
      const releaseInfo = await this.getAppCurrentRelease()
      if (!releaseInfo.version) return
      version = releaseInfo.version
    }

    const files = await readdir(this.downloadAppVersionPath);
    const file = files.find(
      (f) => f.endsWith(`.js`) && f.includes(`-` + version)
    );

    if (!file) return;
    return join(this.staticPrefix, this.basePath, this.versionPath, file)
  }

  async getBrowserExtensionVersion() {
    const res = await this.settingModel.findOne({
      name: 'currentBrowserExtensionVersion',
    });

    return res?.value;
  }

  async downloadBrowserExtension(compat = false, source = '', ip = '') {
    const res = await this.settingModel.findOne({
      name: 'currentBrowserExtensionVersion',
    });

    if (!(res && res.value)) return;

    const files = await readdir(compat ? this.downloadBrowserExtensionCompatPath : this.downloadBrowserExtensionPath);
    const file = files.find(
      (f) => (f.endsWith(`.zip`) || f.endsWith('.crx')) && f.includes(`-` + res.value as string)
    );

    if (!file) return;

    this.recordDownload(compat ? 'browser-extension-compat' : 'browser-extension', `${res.value}`, source, ip);

    return join(this.staticPrefix, this.basePath, compat ? this.browserExtensionCompatPath : this.browserExtensionPath, file)
  }

  async downloadBrowserExtensionVersionJs() {
    const res = await this.settingModel.findOne({
      name: 'currentBrowserExtensionVersion',
    });

    if (!(res && res.value)) return;

    const files = await readdir(this.downloadBrowserExtensionVersionPath);
    const file = files.find(
      (f) => f.endsWith(`.js`) && f.includes(`-` + res.value as string)
    );

    if (!file) return;

    return join(this.staticPrefix, this.basePath, this.browserExtensionVersionPath, file)
  }

  async downloadAppHomeJs() {
    const res = await this.settingModel.findOne({
      name: 'currentAppHomeVersion',
    });

    if (!(res && res.value)) return;

    const files = await readdir(this.downloadAppHomePath);
    const file = files.find(
      (f) => f.endsWith(`.js`) && f.includes(`-` + res.value as string)
    );

    if (!file) return;

    return join(this.staticPrefix, this.basePath, this.appHomePath, file)
  }

  async downloadExtension(id: string, plat: string) {
    const extension = await this.extensionModel.findOne({ id, plat: { $in: [plat, 'universal'] } });
    if (!(extension && extension.path)) return;

    return join(this.staticPrefix, this.basePath, this.extensionPath, extension.path)
  }

  private parseVersion(ver: string) {
    if (typeof ver != 'string') {
      return 0
    }
    const [major, minor, patch] = ver.split('.').map(v => parseInt(v))
    if (major < 256 && minor < 256 && patch < 256) {
      return (major << 16) + (minor << 8) + patch
    }
    return 0
  }

  async extractExtension(file: IFile) {
    const Zip = new JSZip()
    const zip = await Zip.loadAsync(file.buffer)
    const content = await zip.file('package.json').async('string')
    const meta = JSON.parse(content)

    const extension = await this.extensionModel.findOneAndUpdate({
      name: meta.name,
      type: meta.extend.type
    }, {
      name: meta.name,
      displayName: meta.displayName,
      version: this.parseVersion(meta.version),
      author: meta.author,
      description: meta.description,
      path: '',
      type: meta.extend.type
    }, {
      new: true,
      upsert: true
    })

    extension.path = `${extension.type}-${extension.name}-${extension._id}.zip`

    zip.file('package.json', JSON.stringify({
      ...meta,
      _id: extension._id
    }))
    const resultZipBuffer = await zip.generateAsync({ type: 'nodebuffer' })
    const ws = createWriteStream(join(this.downloadExtensionPath, extension.path))
    ws.write(resultZipBuffer)
    ws.close()
    await extension.save()
  }
}
