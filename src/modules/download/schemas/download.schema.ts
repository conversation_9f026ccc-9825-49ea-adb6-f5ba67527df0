
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true, versionKey: false })
export class DownloadEntity {
  @Prop({
    type: String
  })
  name: string;

  @Prop({
    type: String
  })
  version: string;

  @Prop({
    type: String
  })
  source: string;

  @Prop({
    type: String
  })
  ip: string;
}

export const DownloadDatabaseName = 'downloads';
export const DownloadSchema = SchemaFactory.createForClass(DownloadEntity);

export type DownloadDocument = DownloadEntity & Document;
