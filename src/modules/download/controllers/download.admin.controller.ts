import { Controller, Post, UploadedFile, UploadedFiles } from "@nestjs/common";
import { createWriteStream } from "fs";
import { join } from "path";
import { UploadFileMultiple, UploadFileSingle } from "src/common/file/decorators/file.decorator";
import { IFile } from "src/common/file/file.interface";
import { FileRequiredPipe } from "src/common/file/pipes/file.required.pipe";
import { Response } from 'src/common/response/decorators/response.decorator';
import { DownloadService } from "../services/download.service";
import { AuthAdminJwtGuard } from "src/common/auth/decorators/auth.jwt.decorator";

@AuthAdminJwtGuard()
@Controller('/download')
export class DownloadController {

  constructor(
    private readonly downloadService: DownloadService
  ) { }

  @Response('download.app')
  @Post('/app')
  @UploadFileMultiple('files')
  uploadApp(@UploadedFiles(FileRequiredPipe) files: IFile[]) {
    const path = this.downloadService.downloadAppPath
    this.downloadService.writeFilesToPath(files, path)
  }

  @Response('download.browser-extension')
  @Post('/browser-extension')
  @UploadFileMultiple('files')
  uploadBrowserExtension(@UploadedFiles(FileRequiredPipe) files: IFile[]) {
    const path = this.downloadService.downloadBrowserExtensionPath
    this.downloadService.writeFilesToPath(files, path)
  }

  @Response('download.browser-extension-compat')
  @Post('/browser-extension-compat')
  @UploadFileMultiple('files')
  uploadBrowserExtensionCompat(@UploadedFiles(FileRequiredPipe) files: IFile[]) {
    const path = this.downloadService.downloadBrowserExtensionCompatPath
    this.downloadService.writeFilesToPath(files, path)
  }

  @Response('download.app.version')
  @Post('/app/version')
  @UploadFileMultiple('files')
  uploadAppVersion(@UploadedFiles(FileRequiredPipe) files: IFile[]) {
    const path = this.downloadService.downloadAppVersionPath
    this.downloadService.writeFilesToPath(files, path)
  }

  @Response('download.browser-extension.versionJs')
  @Post('/browser-extension/version')
  @UploadFileMultiple('files')
  uploadBrowserExtensionVersion(@UploadedFiles(FileRequiredPipe) files: IFile[]) {
    const path = this.downloadService.downloadBrowserExtensionVersionPath
    this.downloadService.writeFilesToPath(files, path)
  }

  @Response('download.app-home')
  @Post('/app-home')
  @UploadFileMultiple('files')
  uploadAppHome(@UploadedFiles(FileRequiredPipe) files: IFile[]) {
    const path = this.downloadService.downloadAppHomePath
    this.downloadService.writeFilesToPath(files, path)
  }

  @Response('download.extension')
  @Post('/extension')
  @UploadFileMultiple('files')
  async uploadExtensions(@UploadedFiles(FileRequiredPipe) files: IFile[]) {
    await Promise.all(files.map(file => this.downloadService.extractExtension(file)))
  }

}