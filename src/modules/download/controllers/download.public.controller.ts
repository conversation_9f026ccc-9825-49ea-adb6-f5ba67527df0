import { <PERSON>, Get, Param, Query, Redirect, <PERSON><PERSON>, Head<PERSON> } from "@nestjs/common";
import { createReadStream } from "fs";
import { join } from "path";
import { DownloadService } from "../services/download.service";
import { Response } from "express";
import { RequestIp } from "src/common/request/decorators/request.decorator";

@Controller('/download')
export class DownloadPublicController {
  constructor(
    private readonly downloadService: DownloadService,
  ) { }

  @Get('/app')
  async getApp(@Res() res: Response) {
    const path = await this.downloadService.downloadApp()
    res.redirect(path)
  }

  @Get('/app/:platform/:arch')
  async getAppByPlatform(
    @Param('platform') platform: 'win32' | 'darwin',
    @Param('arch') arch: 'x64' | 'arm64',
    @Query('source') source: string,
    @RequestIp() ip: string,
    @Res() res: Response
  ) {
    const path = await this.downloadService.downloadApp(platform, arch, source, ip)
    res.redirect(path)
  }

  @Get('/app/update/:platform/:arch')
  async getAppUpdateByPlatform(
    @Param('platform') platform: 'win32' | 'darwin',
    @Param('arch') arch: 'x64' | 'arm64',
    @Headers('host') host: string,
  ) {
    const releaseInfo = await this.downloadService.getAppCurrentRelease()
    return {
      version: releaseInfo.version,
      releaseDate: releaseInfo.releaseDate,
      force: releaseInfo.force,
      endpoint: `http://${host}/api/v1/public/download/app/${platform}/${arch}?source=update`
    }
  }

  @Get('/browser-extension/version')
  getBrowserExtensionVersion() {
    return this.downloadService.getBrowserExtensionVersion()
  }

  @Get('/browser-extension')
  async getBrowserExtension(
    @Query('source') source: string,
    @RequestIp() ip: string,
    @Res() res: Response
  ) {
    const path = await this.downloadService.downloadBrowserExtension(false, source, ip)
    res.redirect(path)
  }

  @Get('/browser-extension-compat')
  async getBrowserExtensionCompat(
    @Query('source') source: string,
    @RequestIp() ip: string,
    @Res() res: Response
  ) {
    const path = await this.downloadService.downloadBrowserExtension(true, source, ip)
    res.redirect(path)
  }

  @Get('/app/version/js/:version')
  async getAppVersionJs(@Param('version') version: string, @Res() res: Response) {
    const path = await this.downloadService.downloadAppVersionJs(version)
    res.redirect(path)
  }

  @Get('/browser-extension/version/js')
  async getBrowserExtensionVersionJs(@Res() res: Response) {
    const path = await this.downloadService.downloadBrowserExtensionVersionJs()
    res.redirect(path)
  }

  @Get('/app-home/js')
  async getAppHomeJs(@Res() res: Response) {
    const path = await this.downloadService.downloadAppHomeJs()
    res.redirect(path)
  }

}