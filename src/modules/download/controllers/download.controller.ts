import { <PERSON>, Get, Param, Post, Query, Res, UploadedFiles } from "@nestjs/common";
import { Response as ResponseType } from "express";
import { UploadFileMultiple } from "src/common/file/decorators/file.decorator";
import { IFile } from "src/common/file/file.interface";
import { FileRequiredPipe } from "src/common/file/pipes/file.required.pipe";
import { Response } from 'src/common/response/decorators/response.decorator';
import { AuthGiant } from "src/modules/giant/decorators/giant.decorator";
import { DownloadService } from "../services/download.service";

@Controller('/download')
export class DownloadController {

  constructor(
    private readonly downloadService: DownloadService
  ) { }

  @Response('download.app')
  @Post('/app')
  @AuthGiant()
  @UploadFileMultiple('files')
  uploadApp(
    @UploadedFiles(FileRequiredPipe) files: IFile[],
  ) {
    const path = this.downloadService.downloadAppPath
    this.downloadService.writeFilesToPath(files, path)
  }

  @Response('download.app.ready')
  @Post('/app-ready')
  @AuthGiant()
  @UploadFileMultiple('files')
  uploadAppReady(
    @UploadedFiles(FileRequiredPipe) files: IFile[],
  ) {
    const path = this.downloadService.downloadAppReadyPath
    this.downloadService.writeFilesToPath(files, path)
  }

  @Response('download.browser-extension')
  @Post('/browser-extension')
  @AuthGiant()
  @UploadFileMultiple('files')
  uploadBrowserExtension(@UploadedFiles(FileRequiredPipe) files: IFile[]) {
    const path = this.downloadService.downloadBrowserExtensionPath
    this.downloadService.writeFilesToPath(files, path)
  }

  @Response('download.browser-extension')
  @Post('/browser-extension-compat')
  @AuthGiant()
  @UploadFileMultiple('files')
  uploadBrowserExtensionCompat(@UploadedFiles(FileRequiredPipe) files: IFile[]) {
    const path = this.downloadService.downloadBrowserExtensionCompatPath
    this.downloadService.writeFilesToPath(files, path)
  }

  @Response('download.app.version')
  @Post('/app/version')
  @AuthGiant()
  @UploadFileMultiple('files')
  uploadAppVersion(@UploadedFiles(FileRequiredPipe) files: IFile[]) {
    const path = this.downloadService.downloadAppVersionPath
    this.downloadService.writeFilesToPath(files, path)
  }

  @Response('download.browser-extension.versionJs')
  @AuthGiant()
  @Post('/browser-extension/version')
  @UploadFileMultiple('files')
  uploadBrowserExtensionVersion(@UploadedFiles(FileRequiredPipe) files: IFile[]) {
    const path = this.downloadService.downloadBrowserExtensionVersionPath
    this.downloadService.writeFilesToPath(files, path)
  }

  @Response('download.app-home')
  @AuthGiant()
  @Post('/app-home')
  @UploadFileMultiple('files')
  uploadAppHome(@UploadedFiles(FileRequiredPipe) files: IFile[]) {
    const path = this.downloadService.downloadAppHomePath
    this.downloadService.writeFilesToPath(files, path)
  }


  @Get('/extension/:id')
  async getExtension(@Param('id') id: string, @Query('plat') plat: string, @Res() res: ResponseType) {
    // transpile plat
    if (plat?.startsWith('win32')) {
      plat = 'win32'
    } else if (plat !== 'darwin-x64' && plat !== 'darwin-arm64') {
      plat = 'universal'
    }

    const path = await this.downloadService.downloadExtension(id, plat)
    res.redirect(path)
  }

}