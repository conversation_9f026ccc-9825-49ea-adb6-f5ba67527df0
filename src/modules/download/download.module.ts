import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { SettingDatabaseName, SettingEntity, SettingSchema } from "src/common/setting/schemas/setting.schema";
import { PayModule } from "src/gateways/gateway.module";
import { ExtensionDatabaseName, ExtensionEntity, ExtensionSchema } from "../extension/schemas/extension.schema";
import { StatisticsModule } from "../statistics/statistics.module";
import { DownloadService } from "./services/download.service";
import { DownloadDatabaseName, DownloadEntity, DownloadSchema } from "./schemas/download.schema";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: SettingEntity.name,
          schema: SettingSchema,
          collection: SettingDatabaseName
        },
        {
          name: ExtensionEntity.name,
          schema: ExtensionSchema,
          collection: ExtensionDatabaseName
        },
        {
          name: DownloadEntity.name,
          schema: DownloadSchema,
          collection: DownloadDatabaseName
        }
      ],
      DATABASE_CONNECTION_NAME
    ),
    StatisticsModule,
    PayModule,
  ],
  exports: [DownloadService],
  providers: [DownloadService],
})
export class DownloadModule { }