import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { Model } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { ExtensionCreateDto } from "../dtos/extension.create.dto";
import { ExtensionUpdateDto } from "../dtos/extension.update.dto";
import { IExtensionDocument } from "../extension.interface";
import { ExtensionDocument, ExtensionEntity } from "../schemas/extension.schema";
import { IFile } from "src/common/file/file.interface";
import { GiantDocument, GiantEntity } from "src/modules/giant/schemas/giant.schema";
import JSZip from "jszip";
import { GiantService } from "src/modules/giant/services/giant.service";
import { DownloadService } from "src/modules/download/services/download.service";
import { join } from "path";
import { createWriteStream } from "fs";
import { ConfigService } from "@nestjs/config";
import { HelperCosService } from "src/common/helper/services/helper.cos.service";
import { HelperMd2HtmlService } from "src/common/helper/services/helper.md2html.service";
import { JSDOM } from "jsdom";

@Injectable()
export class ExtensionService {
  private readonly env: string

  constructor(
    @DatabaseEntity(ExtensionEntity.name)
    private readonly extensionModel: Model<ExtensionDocument>,
    @DatabaseEntity(GiantEntity.name)
    private readonly giantModel: Model<GiantDocument>,
    private readonly giantService: GiantService,
    private readonly downloadService: DownloadService,
    private readonly configService: ConfigService,
    private readonly helperCosService: HelperCosService,
    private readonly helperMd2HtmlService: HelperMd2HtmlService,
  ) {
    this.env = this.configService.get<string>('app.env')
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IExtensionDocument[]> {
    const extensions = this.extensionModel.find(find)
    if (
      options &&
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      extensions.limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      extensions.sort(options.sort)
    }

    return extensions.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.extensionModel.countDocuments(find)
  }

  async exists(id: string) {
    const exists = await this.extensionModel.exists({
      id
    });
    return !!exists;
  }

  async create(data: ExtensionCreateDto) {
    const create: ExtensionDocument = new this.extensionModel(data);
    return create.save();
  }

  async deleteById(id: string) {
    return this.extensionModel.findByIdAndDelete(id)
  }

  async findByExtensionId(id: string) {
    return this.extensionModel.find({ id })
  }

  async findByIdAndPlat(id: string, plat: string) {
    return this.extensionModel.findOne({ id, plat: { $in: [plat, 'universal'] } })
  }

  async freeze(id: string, status: boolean) {
    const doc = await this.extensionModel.findById(id)
    if (doc) {
      doc.disabled = status
      return doc.save()
    }
  }

  async updateOneById(id: string, extensionUpdateDto: ExtensionUpdateDto) {
    const update: ExtensionDocument = await this.extensionModel.findOne({
      id
    });
    update.mark = extensionUpdateDto.mark || update.mark
    return update.save()
  }

  parseVersion(ver: string) {
    if (typeof ver != 'string') {
      return 0
    }
    const [major, minor, patch] = ver.split('.').map(v => parseInt(v))
    if (major < 256 && minor < 256 && patch < 256) {
      return (major << 16) + (minor << 8) + patch
    }
    return 0
  }

  async uploadExtension(key: string, file: IFile, plat = 'universal') {
    const Zip = new JSZip()
    const zip = await Zip.loadAsync(file.buffer)
    const packageJson = await zip.file('package.json').async('string')
    const meta = JSON.parse(packageJson)

    // verify id
    const [developerId, extensionId] = meta.id.split('.')
    const isValid = await this.giantService.verifyKeyAndDeveloperId(key, developerId)
    if ((!isValid || !extensionId) && this.env !== 'development') {
      throw new InternalServerErrorException('Invalid extension id')
    }

    // make sure 'engineVersion' exists
    if (!meta.engineVersion) {
      throw new InternalServerErrorException('engineVersion is required')
    }

    // make sure the 'README.md' exists
    if (!zip.file('README.md')) {
      throw new InternalServerErrorException('README.md is required')
    }

    // make sure the icon exists
    if (!meta.icon && !zip.file(meta.icon)) {
      throw new InternalServerErrorException('icon is required')
    }

    // verify 'universal' and 'specific' can't exists same time
    const metas = await this.findByExtensionId(meta.id)
    if (metas.some(m => m.plat === 'universal') && plat !== 'universal') {
      throw new InternalServerErrorException('universal and specific can not exists same time')
    } else if (metas.some(m => m.plat !== 'universal') && plat === 'universal') {
      throw new InternalServerErrorException('universal and specific can not exists same time')
    }

    let extension = await this.findByIdAndPlat(meta.id, plat)
    const version = this.parseVersion(meta.version)

    if (extension && version <= extension.version) {
      throw new InternalServerErrorException('extension already exists, you may need to update version')
    }

    // get translation
    let lng = {}
    if (meta.i18n) {
      const i18n = await zip.file(meta.i18n).async('string')
      lng = JSON.parse(i18n)
    }

    // get icon
    let iconUrl = ''
    const icon = await zip.file(meta.icon).async('nodebuffer')
    const iconUploadResult = await this.helperCosService.uploadFile(`extension/${meta.id}/${plat}/${meta.icon}`, icon)
    iconUrl = iconUploadResult.Location

    // get html
    const readmemd = await zip.file('README.md').async('string')
    let html = await this.helperMd2HtmlService.parse(readmemd)

    // replace img src
    const dom = new JSDOM(html)
    const imgs = [...dom.window.document.querySelectorAll('img')]
    for (const img of imgs) {
      let src = img.getAttribute('src')
      if (src && !src.startsWith('http')) {
        src = src.replace(/^[\.\/]*/, '')
        const buffer = await zip.file(src).async('nodebuffer')
        const uploadResult = await this.helperCosService.uploadFile(`extension/${meta.id}/${plat}/${src}`, buffer)
        img.setAttribute('src', `//${uploadResult.Location}`)
      }
    }

    html = dom.window.document.body.innerHTML

    extension = extension || new this.extensionModel({})
    extension.id = meta.id
    extension.name = meta.name
    extension.displayName = meta.displayName
    extension.version = version
    extension.engineVersion = this.parseVersion(meta.engineVersion)
    extension.author = meta.author
    extension.description = meta.description
    extension.plat = plat
    extension.type = meta.extend.type
    extension.lng = lng
    extension.icon = iconUrl
    extension.html = html
    extension.path = `${extension.type}-${extension.id}-${extension.plat}-${meta.version}.krx`

    const resultZipBuffer = await zip.generateAsync({ type: 'nodebuffer', platform: plat === 'win32' ? 'DOS' : 'UNIX' })
    const ws = createWriteStream(join(this.downloadService.downloadExtensionPath, extension.path))
    ws.write(resultZipBuffer)
    ws.close()
    await extension.save()
  }
}