import { Body, Controller, Get, Post, Query, UploadedFile, Version } from "@nestjs/common";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { ResponsePaging, Response } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { ExtensionListDto } from "../dtos/extension.list.dto";
import { ExtensionListSerializtion } from "../serializations/extension.list.serialization";
import { ExtensionService } from "../services/extension.service";
import { UploadFileSingle } from "src/common/file/decorators/file.decorator";
import { FileRequiredPipe } from "src/common/file/pipes/file.required.pipe";
import { IFile } from "src/common/file/file.interface";
import { GiantService } from "src/modules/giant/services/giant.service";

@Controller('/extension')
export class ExtensionPublicController {
  constructor(
    private readonly extensionService: ExtensionService,
    private readonly paginationService: PaginationService,
    private readonly giantService: GiantService,
  ) { }


  @ResponsePaging('extension.list', {
    classSerialization: ExtensionListSerializtion
  })
  @Post('/list')
  async list(
    @Body()
    {
      current,
      pageSize,
      sort,
      fields,
      availableSort,
      availableSearch,
    }: ExtensionListDto,
    @Query('plat') plat: string, // need to transpile
    @Query('lng') lng: string,
    // @Query('engineVersion') rawEngineVersion: string,
  ): Promise<IResponsePaging> {
    lng = lng || 'zh'

    // transpile plat
    if (plat?.startsWith('win32')) {
      plat = 'win32'
    } else if (plat !== 'darwin-x64' && plat !== 'darwin-arm64') {
      plat = 'universal'
    }

    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = {
      ...fields,
      disabled: false,
      plat: plat || 'universal',
      // engineVersion: {
      //   $lte: this.extensionService.parseVersion(rawEngineVersion)
      // }
    }

    let extensions: any[] = await this.extensionService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })

    extensions = extensions.map(ext => {
      return {
        ...ext,
        displayName: ext.lng[lng]?.[ext.displayName] || ext.displayName,
        description: ext.lng[lng]?.[ext.description] || ext.description,
      }
    })

    const total = await this.extensionService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: extensions
    }
  }

  @Response('download.extension')
  @Post('/publish')
  @Version('1')
  @UploadFileSingle('extension')
  async uploadExtensionsV1(
    @UploadedFile(FileRequiredPipe) extension: IFile,
    @Body('key') key: string,
  ) {
    const verifyResult = await this.giantService.verifyDeveloper(key)

    if (verifyResult.status === 'success') {
      await this.extensionService.uploadExtension(key, extension)
      return {
        status: 'success'
      }
    }

    return {
      status: 'error',
    }
  }

  @Response('download.extension')
  @Post('/publish')
  @Version('2')
  @UploadFileSingle('extension')
  async uploadExtensionsV2(
    @UploadedFile(FileRequiredPipe) extension: IFile,
    @Body('key') key: string,
    @Body('plat') plat: string
  ) {
    const verifyResult = await this.giantService.verifyDeveloper(key)

    if (verifyResult.status === 'success') {
      await this.extensionService.uploadExtension(key, extension, plat)
      return {
        status: 'success'
      }
    }

    return {
      status: 'error',
    }
  }

  @Response('extension.updates')
  @Post('/updates')
  async getUpdate(
    @Body() versionMap: Record<string, number>,
    @Query('plat') plat: string, // need to transpile
    @Query('engineVersion') rawEngineVersion: string
  ) {
    const engineVersion = this.extensionService.parseVersion(rawEngineVersion)
    // transpile plat
    if (plat?.startsWith('win32')) {
      plat = 'win32'
    } else if (plat !== 'darwin-x64' && plat !== 'darwin-arm64') {
      plat = 'universal'
    }

    const exts = await Promise.all(Object.entries(versionMap).map(async ([id, version]) => {
      const ext = await this.extensionService.findByIdAndPlat(id, plat || 'universal')
      if (ext) {
        return {
          id: ext.id,
          version: ext.version,
          oldVersion: version,
          engineVersion: ext.engineVersion
        }
      }
      return null
    }))

    const updates = exts.filter(ext => ext)
      .filter(ext => ext.engineVersion && engineVersion && ext.engineVersion <= engineVersion)
      .filter(ext => ext.version > ext.oldVersion)

    return {
      updates: updates.map(ext => ({ id: ext.id, version: ext.version }))
    }
  }

}