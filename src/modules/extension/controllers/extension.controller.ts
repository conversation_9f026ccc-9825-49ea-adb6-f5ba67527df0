import { BadRequestException, Body, Controller, Delete, Get, InternalServerErrorException, Param, Post, Put, Query, UploadedFile } from "@nestjs/common";
import { ENUM_ERROR_STATUS_CODE_ERROR } from "src/common/error/constants/error.status-code.constant";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { ResponsePaging, Response } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { AuthGiant } from "src/modules/giant/decorators/giant.decorator";
import { ExtensionListDto } from "../dtos/extension.list.dto";
import { ExtensionUpdateDto } from "../dtos/extension.update.dto";
import { ExtensionListSerializtion } from "../serializations/extension.list.serialization";
import { ExtensionService } from "../services/extension.service";
import { isValidObjectId } from "mongoose";
import { UploadFileSingle } from "src/common/file/decorators/file.decorator";
import { FileRequiredPipe } from "src/common/file/pipes/file.required.pipe";
import { IFile } from "src/common/file/file.interface";

@AuthGiant()
@Controller('/extension')
export class ExtensionController {
  constructor(
    private readonly extensionService: ExtensionService,
    private readonly paginationService: PaginationService,
  ) { }

  // deprecated
  @Response('extension.updates')
  @AuthGiant()
  @Post('/updates')
  async getUpdate(
    @Body() versionMap: Record<string, number>
  ) {
    const exts = await Promise.all(Object.entries(versionMap).map(async ([id, version]) => {
      if (isValidObjectId(id)) {
        const ext = await this.extensionService.findByIdAndPlat(id, 'universal')
        if (ext) {
          return {
            id: ext.id,
            version: ext.version,
            oldVersion: version
          }
        }
      }
      return null
    }))
    const updates = exts.filter(ext => ext).filter(ext => ext.version > ext.oldVersion)
    return updates.reduce((acc, ext) => ({ ...acc, [ext.id]: ext.version }), {})
  }
}