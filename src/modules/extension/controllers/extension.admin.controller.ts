import { Body, Controller, Delete, Get, InternalServerErrorException, Param, Post, Put, Query } from "@nestjs/common";
import { AuthAdminJwtGuard } from "src/common/auth/decorators/auth.jwt.decorator";
import { ENUM_ERROR_STATUS_CODE_ERROR } from "src/common/error/constants/error.status-code.constant";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { Response, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { ExtensionListDto } from "../dtos/extension.list.dto";
import { ExtensionUpdateDto } from "../dtos/extension.update.dto";
import { ExtensionListSerializtion } from "../serializations/extension.list.serialization";
import { ExtensionService } from "../services/extension.service";

@AuthAdminJwtGuard()
@Controller('/extension')
export class ExtensionController {
  constructor(
    private readonly extensionService: ExtensionService,
    private readonly paginationService: PaginationService,
  ) { }

  @ResponsePaging('extension.list', {
    classSerialization: ExtensionListSerializtion
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      sort,
      fields,
      availableSort,
      availableSearch,
    }: ExtensionListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = {
      ...fields
    }

    const extensions = await this.extensionService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })
    const total = await this.extensionService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: extensions
    }
  }

  @Response('extension.create')
  @Post('/create')
  async create() {
  }

  @Response('extension.update')
  @Put('/update/:id')
  async update(
    @Param('id') id: string,
    @Body() extensionUpdateDto: ExtensionUpdateDto
  ) {
    try {
      await this.extensionService.updateOneById(id, extensionUpdateDto);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('extension.freeze')
  @Put('/freeze/:id/:status')
  async freeze(
    @Param('id') id: string,
    @Param('status') status: string
  ) {
    try {
      return await this.extensionService.freeze(id, status == '1');
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('extension.reset')
  @Delete('/delete/:id')
  async delete(
    @Param('id') id: string
  ) {
    try {
      return await this.extensionService.deleteById(id);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

}