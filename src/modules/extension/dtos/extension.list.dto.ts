import { PaginationListAbstract } from "src/common/pagination/abstracts/pagination.abstract";
import { PaginationAvailableSearch, PaginationAvailableSort, PaginationFields, PaginationPage, PaginationPerPage, PaginationSearch, PaginationSort } from "src/common/pagination/decorators/pagination.decorator";
import { IPaginationSort } from "src/common/pagination/pagination.interface";
import { EXTENSION_DEFAULT_AVAILABLE_SEARCH, EXTENSION_DEFAULT_PAGE, EXTENSION_DEFAULT_PER_PAGE, EXTENSION_DEFAULT_SORT, EXTENSION_DEFAULT_AVAILABLE_SORT } from "../constants/extension.list.constant";

export class ExtensionListDto implements PaginationListAbstract {

  @PaginationFields([
    {
      key: 'keyword',
      fuzzy: true,
      or: ['name', 'displayName', 'author']
    },
    'type',
    'mark',
  ])
  readonly fields: Record<string, any>

  @PaginationAvailableSearch(EXTENSION_DEFAULT_AVAILABLE_SEARCH)
  readonly availableSearch: string[];

  @PaginationPage(EXTENSION_DEFAULT_PAGE)
  readonly current: number;

  @PaginationPerPage(EXTENSION_DEFAULT_PER_PAGE)
  readonly pageSize: number;

  @PaginationSort(EXTENSION_DEFAULT_SORT, EXTENSION_DEFAULT_AVAILABLE_SORT)
  readonly sort: IPaginationSort;

  @PaginationAvailableSort(EXTENSION_DEFAULT_AVAILABLE_SORT)
  readonly availableSort: string[];
}