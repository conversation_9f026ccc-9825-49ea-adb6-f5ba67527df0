import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true, versionKey: false })
export class ExtensionEntity {
  @Prop({
    required: true,
    index: true,
    type: String
  })
  id: string;

  @Prop({
    required: true,
  })
  name: string;

  @Prop({
    required: true,
  })
  displayName: string;

  @Prop({
    required: true,
  })
  version: number;

  @Prop({
    required: true,
  })
  author: string;

  @Prop({
    required: true,
  })
  description: string;

  @Prop({
    type: String,
    enum: ['universal', 'win32', 'darwin-x64', 'darwin-arm64']
  })
  plat: string;

  @Prop({
    required: true,
  })
  path: string;

  @Prop({
    required: true,
  })
  type: string;

  @Prop({
    default: 'none'
  })
  mark: string;

  @Prop({
    default: 0
  })
  useNumber: number;

  @Prop({
    default: 0
  })
  rateNumber: number;

  @Prop({
    default: 0
  })
  rate: number;

  @Prop({
    default: () => []
  })
  tags: string[];

  @Prop({
    required: true
  })
  engineVersion: number;

  @Prop({
    type: String,
  })
  icon: string

  @Prop({
    type: String
  })
  html: string

  @Prop({
    type: Object,
    default: () => ({}),
  })
  lng: Record<string, any>

  @Prop({
    default: false
  })
  disabled: boolean
}

export const ExtensionDatabaseName = 'extensions';
export const ExtensionSchema = SchemaFactory.createForClass(ExtensionEntity);

export type ExtensionDocument = ExtensionEntity & Document;
