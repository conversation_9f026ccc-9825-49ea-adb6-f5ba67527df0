import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { GiantModule } from "../giant/giant.module";
import { GiantDatabaseName, GiantEntity, GiantSchema } from "../giant/schemas/giant.schema";
import { ExtensionDatabaseName, ExtensionEntity, ExtensionSchema } from "./schemas/extension.schema";
import { ExtensionService } from "./services/extension.service";
import { DownloadModule } from "../download/download.module";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: ExtensionEntity.name,
          schema: ExtensionSchema,
          collection: ExtensionDatabaseName
        },
        {
          name: GiantEntity.name,
          schema: GiantSchema,
          collection: GiantDatabaseName
        }
      ],
      DATABASE_CONNECTION_NAME
    ),
    GiantModule,
    DownloadModule,
  ],
  exports: [ExtensionService],
  providers: [ExtensionService],
  controllers: []
})
export class ExtensionModule { }