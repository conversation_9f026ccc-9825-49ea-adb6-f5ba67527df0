import { Exclude, Type } from "class-transformer";

export class ExtensionListSerializtion {
  @Type(() => String)
  readonly _id: string
  @Type(() => String)
  readonly id: string

  readonly name: string
  readonly displayName: string
  readonly version: number
  @Exclude()
  readonly path: string
  readonly author: string
  readonly description: string
  readonly type: string
  readonly mark: string
  readonly useNumber: number
  readonly rateNumber: number
  readonly rate: number
  readonly tags: string[]
  readonly status: string

  @Exclude()
  readonly lng: Record<string, any>
  @Exclude()
  readonly icon: string
  @Exclude()
  readonly html: string
}