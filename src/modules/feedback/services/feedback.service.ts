import { Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { IFeedbackDocument } from "../feedback.interface";
import { FeedbackDocument, FeedbackEntity } from "../schemas/feedback.schema";

@Injectable()
export class FeedbackService {
  constructor(
    @DatabaseEntity(FeedbackEntity.name)
    private readonly feedbackModel: Model<FeedbackDocument>
  ) {
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IFeedbackDocument[]> {
    const feedbacks = this.feedbackModel.find(find)
    if (
      options &&
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      feedbacks.limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      feedbacks.sort(options.sort)
    }

    return feedbacks.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.feedbackModel.countDocuments(find)
  }

  async create(key: string, category: string, content: string) {
    const create = new this.feedbackModel({
      key,
      category,
      content
    })
    await create.save()
    return create
  }

}