import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true, versionKey: false })
export class FeedbackEntity {
  @Prop({
    required: true,
  })
  key: string;

  @Prop({
    required: true,
  })
  category: string;

  @Prop({
    required: true,
  })
  content: string;
}

export const FeedbackDatabaseName = 'feedbacks';
export const FeedbackSchema = SchemaFactory.createForClass(FeedbackEntity);

export type FeedbackDocument = FeedbackEntity & Document;
