import { Type } from 'class-transformer';
import {
  IsNotEmpty, IsString, <PERSON><PERSON><PERSON>th,
  <PERSON><PERSON>ength
} from 'class-validator';
import { SpecifiedString } from 'src/common/request/validations/request.specified-string.validation';

export class FeedbackCreateDto {
  @IsString()
  @IsNotEmpty()
  @Type(() => String)
  @SpecifiedString(['bug', 'feature'])
  readonly category: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(9)
  @MaxLength(1000)
  @Type(() => String)
  readonly content: string;
}
