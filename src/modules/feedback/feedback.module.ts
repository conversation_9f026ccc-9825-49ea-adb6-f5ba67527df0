import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { FeedbackDatabaseName, FeedbackEntity, FeedbackSchema } from "./schemas/feedback.schema";
import { FeedbackService } from "./services/feedback.service";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: FeedbackEntity.name,
          schema: FeedbackSchema,
          collection: FeedbackDatabaseName
        }
      ],
      DATABASE_CONNECTION_NAME
    )
  ],
  exports: [FeedbackService],
  providers: [FeedbackService],
  controllers: []
})
export class FeedbackModule { }