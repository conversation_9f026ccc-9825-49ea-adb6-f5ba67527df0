import { Controller, Get, Query } from "@nestjs/common";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { FeedbackListDto } from "../dtos/feedback.list.dto";
import { FeedbackListSerializtion } from "../serializations/feedback.list.serialization";
import { FeedbackService } from "../services/feedback.service";

@Controller('/feedback')
export class FeedbackController {
  constructor(
    private readonly feedbackService: FeedbackService,
    private readonly paginationService: PaginationService,
  ) { }


  @ResponsePaging('feedback.list', {
    classSerialization: FeedbackListSerializtion
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      sort,
      search,
      availableSort,
      availableSearch,
    }: FeedbackListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = {
      ...search
    }

    const orders = await this.feedbackService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })
    const total = await this.feedbackService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: orders
    }
  }
}