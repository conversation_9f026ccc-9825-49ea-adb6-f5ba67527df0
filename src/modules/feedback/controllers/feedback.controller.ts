import { Body, Controller, Get, Post, Query } from "@nestjs/common";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { FeedbackListDto } from "../dtos/feedback.list.dto";
import { FeedbackListSerializtion } from "../serializations/feedback.list.serialization";
import { FeedbackService } from "../services/feedback.service";
import { Response } from "src/common/response/decorators/response.decorator";
import { AuthGiant, GetGiant, OptionalGiant } from "src/modules/giant/decorators/giant.decorator";
import { GiantDocument } from "src/modules/giant/schemas/giant.schema";
import { FeedbackCreateDto } from "../dtos/feedback.create.dto";
import { UploadFileMultiple } from "src/common/file/decorators/file.decorator";

@Controller('/feedback')
export class FeedbackController {
  constructor(
    private readonly feedbackService: FeedbackService,
    private readonly paginationService: PaginationService,
  ) { }

  @Response('feedback')
  @Post()
  @OptionalGiant()
  @UploadFileMultiple('images')
  async feedback(@Body() feedbackCreateDto: FeedbackCreateDto, @GetGiant() giant?: GiantDocument) {
    let key = 'anonymous'
    if (giant) {
      key = giant.key
    }
    const create = await this.feedbackService.create(key, feedbackCreateDto.category, feedbackCreateDto.content)
    return {
      _id: create._id
    }
  }

  @Response('feedback.url')
  @Post('/url')
  @AuthGiant()
  async reportUrl(@GetGiant() giant: GiantDocument, @Body('url') url: string) {
    console.log(giant.key)
    console.log(url)
    const create = await this.feedbackService.create(giant.key, 'url', url)
    return {
      _id: create._id
    }
  }

}