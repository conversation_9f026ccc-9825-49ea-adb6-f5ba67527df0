import { Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { IReportDocument } from "../report.interface";
import { ReportDocument, ReportEntity } from "../schemas/report.schema";


@Injectable()
export class ReportService {
  constructor(
    @DatabaseEntity(ReportEntity.name)
    private readonly reportModel: Model<ReportDocument>,
  ) {
  }

  async findById(id: string) {
    return await this.reportModel.findById(id)
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IReportDocument[]> {
    const feedbacks = this.reportModel.find(find)
    if (
      options &&
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      feedbacks.limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      feedbacks.sort(options.sort)
    }

    return feedbacks.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.reportModel.countDocuments(find)
  }

  async create({
    key, category, content, ip, machineId, version, system,
  }: {
    key: string, machineId: string, ip: string, category: string, content: string, version?: string, system?: string
  }) {
    const create = new this.reportModel({
      key,
      machineId,
      ip,
      category,
      content,
      system,
      version,
    })
    await create.save()
    return create
  }

  async delete(ids: string[]) {
    const result = await this.reportModel.deleteMany({
      _id: {
        $in: ids
      }
    })
    return result
  }

  async deleteBySearch(search: Record<string, any>) {
    const result = await this.reportModel.deleteMany(search)

    return result
  }

  async deleteMany(content: string) {
    const result = await this.reportModel.deleteMany({
      content: {
        $regex: content
      }
    })

    return result
  }

  async stackTrace(content: string) {
    const { default: clipboardy } = await import('clipboardy')
    clipboardy.writeSync(content)
  }
}