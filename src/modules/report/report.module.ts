import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { ReportDatabaseName, ReportEntity, ReportSchema } from "./schemas/report.schema";
import { ReportService } from "./services/report.service";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: ReportEntity.name,
          schema: ReportSchema,
          collection: ReportDatabaseName
        }
      ],
      DATABASE_CONNECTION_NAME
    )
  ],
  exports: [ReportService],
  providers: [ReportService],
  controllers: []
})
export class ReportModule { }