import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true, versionKey: false })
export class ReportEntity {
  @Prop({
    required: true,
  })
  key: string;

  @Prop({
    type: String,
  })
  machineId: string;

  @Prop({
    type: String,
  })
  ip: string;

  @Prop({
    type: String
  })
  version: string;

  @Prop({
    type: String
  })
  system: string;

  @Prop({
    required: true,
  })
  category: string;

  @Prop({
    required: true,
  })
  content: string;
}

export const ReportDatabaseName = 'reports';
export const ReportSchema = SchemaFactory.createForClass(ReportEntity);

export type ReportDocument = ReportEntity & Document;
