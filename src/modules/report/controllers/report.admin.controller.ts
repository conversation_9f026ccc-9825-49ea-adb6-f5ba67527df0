import { Body, Controller, Delete, Get, Param, Post, Query } from "@nestjs/common";
import { Response } from "src/common/response/decorators/response.decorator";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { ReportListDto } from "../dtos/report.list.dto";
import { ReportListSerializtion } from "../serializations/report.list.serialization";
import { ReportService } from "../services/report.service";
import { AuthAdminJwtGuard } from "src/common/auth/decorators/auth.jwt.decorator";

@AuthAdminJwtGuard()
@Controller('/report')
export class ReportController {
  constructor(
    private readonly reportService: ReportService,
    private readonly paginationService: PaginationService,
  ) { }


  @ResponsePaging('report.list', {
    classSerialization: ReportListSerializtion
  })
  @Get('/list')
  async list(
    @Query()
    {
      fields,
      current,
      pageSize,
      sort,
      search,
      availableSort,
      availableSearch,
    }: ReportListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = {
      ...fields
    }

    const reports = await this.reportService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })
    const total = await this.reportService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: reports
    }
  }

  @Response('report.delete')
  @Post('/delete')
  async deleteReports(@Body() ids: string[]) {
    return await this.reportService.delete(ids)
  }

  @Response('report.delete')
  @Delete('/delete/:id')
  async deleteReport(@Param('id') id: string) {
    return await this.reportService.delete([id])
  }

  @Response('report.deleteMany')
  @Post('/deleteMany')
  async deleteBatchReport(@Body('content') content: string) {
    return await this.reportService.deleteMany(content)
  }

  @Response('report.deleteBySearch')
  @Post('/deleteBySearch')
  async deleteBySearch(
    @Query()
    {
      fields,
    }: ReportListDto
  ) {
    return await this.reportService.deleteBySearch(fields)
  }

  @Response('report.stack')
  @Get('/stack/:id')
  async getStack(@Param('id') id: string) {
    const report = await this.reportService.findById(id)
    if (report) {
      const content = JSON.parse(report.content)
      if (content?.error?.stack) {
        return {
          stack: ''
        }
      }
    }
    return {
      stack: ''
    }
  }
}