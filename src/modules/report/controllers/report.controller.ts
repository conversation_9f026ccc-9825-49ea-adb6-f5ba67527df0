import { Body, Controller, Post } from "@nestjs/common";
import { UploadFileMultiple } from "src/common/file/decorators/file.decorator";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { Response } from "src/common/response/decorators/response.decorator";
import { GetGiant, OptionalGiant } from "src/modules/giant/decorators/giant.decorator";
import { GiantDocument } from "src/modules/giant/schemas/giant.schema";
import { ReportCreateDto } from "../dtos/report.create.dto";
import { ReportService } from "../services/report.service";
import { RequestIp } from "src/common/request/decorators/request.decorator";

@Controller('/report')
export class ReportController {
  constructor(
    private readonly reportService: ReportService,
    private readonly paginationService: PaginationService,
  ) { }

  @Response('report')
  @Post()
  @OptionalGiant()
  @UploadFileMultiple('images')
  async feedback(
    @Body() reportCreateDto: ReportCreateDto,
    @RequestIp() ip: string,
    @GetGiant() giant?: GiantDocument
  ) {
    let key = 'anonymous'
    if (giant) {
      key = giant.key
    }
    // const create = await this.reportService.create(key, reportCreateDto.category, reportCreateDto.content)
    const create = await this.reportService.create({
      key,
      category: reportCreateDto.category,
      content: reportCreateDto.content,
      machineId: reportCreateDto.machineId,
      ip,
    })
    return {
      _id: create._id
    }
  }

  @Response('report.brokenUrl')
  @OptionalGiant()
  @Post('/brokenUrl')
  async reportBrokenUrl(
    @GetGiant() giant: GiantDocument,
    @Body('url') url: string,
    @Body('machineId') machineId: string,
    @Body('version') version: string,
    @Body('system') system: string,
    @RequestIp() ip: string
  ) {
    if (!url) {
      return {
        _id: 0
      }
    }

    let key = 'anonymous'
    if (giant) {
      key = giant.key
    }
    const create = await this.reportService.create({
      key,
      category: 'url',
      content: url,
      ip,
      machineId,
      version,
      system,
    })
    return {
      _id: create._id
    }
  }

  @Response('report.brokenProgram')
  @OptionalGiant()
  @Post('/brokenProgram')
  async reportBrokenProgram(
    @GetGiant() giant: GiantDocument,
    @Body('content') content: string,
    @Body('machineId') machineId: string,
    @Body('version') version: string,
    @Body('system') system: string,
    @RequestIp() ip: string,
    @Body('module') module?: string,
  ) {
    if (!content) {
      return {
        _id: 0
      }
    }

    let key = 'anonymous'
    if (giant) {
      key = giant.key
    }
    const create = await this.reportService.create({
      key,
      category: module || 'program',
      content,
      version,
      system,
      ip,
      machineId
    })
    return {
      _id: create._id
    }
  }

}