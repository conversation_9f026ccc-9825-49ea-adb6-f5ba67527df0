import { PaginationListAbstract } from "src/common/pagination/abstracts/pagination.abstract";
import { PaginationAvailableSearch, PaginationAvailableSort, PaginationFields, PaginationPage, PaginationPerPage, PaginationSearch, PaginationSort } from "src/common/pagination/decorators/pagination.decorator";
import { IPaginationSort } from "src/common/pagination/pagination.interface";
import { FEEDBACK_DEFAULT_AVAILABLE_SEARCH, FEEDBACK_DEFAULT_PAGE, FEEDBACK_DEFAULT_PER_PAGE, FEEDBACK_DEFAULT_SORT, FEEDBACK_DEFAULT_AVAILABLE_SORT } from "../constants/report.list.constant";

export class ReportListDto implements PaginationListAbstract {
  @PaginationFields([
    'machineId',
    'version',
    'system',
    {
      key: 'content',
      fuzzy: true,
    },
  ])
  readonly fields: Record<string, any>

  @PaginationSearch(FEEDBACK_DEFAULT_AVAILABLE_SEARCH)
  readonly search: Record<string, any>

  @PaginationAvailableSearch(FEEDBACK_DEFAULT_AVAILABLE_SEARCH)
  readonly availableSearch: string[];

  @PaginationPage(FEEDBACK_DEFAULT_PAGE)
  readonly current: number;

  @PaginationPerPage(FEEDBACK_DEFAULT_PER_PAGE)
  readonly pageSize: number;

  @PaginationSort(FEEDBACK_DEFAULT_SORT, FEEDBACK_DEFAULT_AVAILABLE_SORT)
  readonly sort: IPaginationSort;

  @PaginationAvailableSort(FEEDBACK_DEFAULT_AVAILABLE_SORT)
  readonly availableSort: string[];
}