import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { OrderDatabaseName, OrderEntity, OrderSchema } from "./schemas/order.schema";
import { OrderService } from "./services/order.service";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: OrderEntity.name,
          schema: OrderSchema,
          collection: OrderDatabaseName
        }
      ],
      DATABASE_CONNECTION_NAME
    )
  ],
  exports: [OrderService],
  providers: [OrderService],
  controllers: []
})
export class OrderModule {}