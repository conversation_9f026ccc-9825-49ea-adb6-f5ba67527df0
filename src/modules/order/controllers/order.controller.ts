import { Controller, Get, Query } from "@nestjs/common";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { OrderListDto } from "../dtos/order.list.dto";
import { OrderListSerializtion } from "../serializations/order.list.serialization";
import { OrderService } from "../services/order.service";
import { AuthAdminJwtGuard } from "src/common/auth/decorators/auth.jwt.decorator";

@AuthAdminJwtGuard()
@Controller('/order')
export class OrderController {
  constructor(
    private readonly orderService: OrderService,
    private readonly paginationService: PaginationService,
  ) { }


  @ResponsePaging('order.list', {
    classSerialization: OrderListSerializtion
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      sort,
      search,
      availableSort,
      availableSearch,
    }: OrderListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = {
      ...search
    }

    const orders = await this.orderService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })
    const total = await this.orderService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: orders
    }
  }

}