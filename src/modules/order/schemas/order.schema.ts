import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";


@Schema({ timestamps: true, versionKey: false })
export class OrderEntity {
  @Prop({
    required: true
  })
  key: string
  @Prop({
    required: true
  })
  ip: string
  @Prop({
    required: true
  })
  machineId: string
  @Prop({
    required: true
  })
  price: number
  @Prop({
    required: true
  })
  days: number
  @Prop({
    required: true
  })
  status: string
}

export const OrderDatabaseName = 'orders';
export const OrderSchema = SchemaFactory.createForClass(OrderEntity);

export type OrderDocument = OrderEntity & Document;
