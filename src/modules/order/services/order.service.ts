import { Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { IOrderDocument } from "../order.interface";
import { OrderDocument, OrderEntity } from "../schemas/order.schema";

@Injectable()
export class OrderService {
  constructor(
    @DatabaseEntity(OrderEntity.name)
    private readonly orderModel: Model<OrderDocument>
  ) {
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IOrderDocument[]> {
    const orders = this.orderModel.find(find)
    if (
      options && 
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      orders.limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      orders.sort(options.sort)
    }

    return orders.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.orderModel.countDocuments(find)
  }

}