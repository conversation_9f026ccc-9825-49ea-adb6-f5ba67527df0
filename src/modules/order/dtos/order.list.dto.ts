import { PaginationListAbstract } from "src/common/pagination/abstracts/pagination.abstract";
import { PaginationAvailableSearch, PaginationAvailableSort, PaginationPage, PaginationPerPage, PaginationSearch, PaginationSort } from "src/common/pagination/decorators/pagination.decorator";
import { IPaginationSort } from "src/common/pagination/pagination.interface";
import { ORDER_DEFAULT_AVAILABLE_SEARCH, ORDER_DEFAULT_AVAILABLE_SORT, ORDER_DEFAULT_PAGE, ORDER_DEFAULT_PER_PAGE, ORDER_DEFAULT_SORT } from "../constants/order.list.constant";

export class OrderListDto implements PaginationListAbstract {
  @PaginationSearch(ORDER_DEFAULT_AVAILABLE_SEARCH)
  readonly search: Record<string, any>

  @PaginationAvailableSearch(ORDER_DEFAULT_AVAILABLE_SEARCH)
  readonly availableSearch: string[];

  @PaginationPage(ORDER_DEFAULT_PAGE)
  readonly current: number;

  @PaginationPerPage(ORDER_DEFAULT_PER_PAGE)
  readonly pageSize: number;

  @PaginationSort(ORDER_DEFAULT_SORT, ORDER_DEFAULT_AVAILABLE_SORT)
  readonly sort: IPaginationSort;

  @PaginationAvailableSort(ORDER_DEFAULT_AVAILABLE_SORT)
  readonly availableSort: string[];
}