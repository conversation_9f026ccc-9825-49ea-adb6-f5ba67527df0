import { BadRequestException, Body, Controller, Delete, Get, InternalServerErrorException, Param, Post, Put, Query, UploadedFile } from "@nestjs/common";
import { ENUM_ERROR_STATUS_CODE_ERROR } from "src/common/error/constants/error.status-code.constant";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { Response, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { ENUM_PAYCONFIG_STATUS_CODE_ERROR } from "src/modules/payconfig/constants/payconfig.status-code.constant";
import { ENUM_PAYCODE_STATUS_CODE_ERROR } from "../constants/paycode.status-code.constant";
import { PaycodeCreateDto } from "../dtos/paycode.create.dto";
import { PaycodeListDto } from "../dtos/paycode.list.dto";
import { PaycodeListSerializtion } from "../serializations/paycode.list.serialization";
import { PaycodeService } from "../services/paycode.service";
import { UploadFileSingle } from 'src/common/file/decorators/file.decorator';
import { QrcodeReadPipe } from "src/common/qrcode/pipes/qrcode.pipe";
import { IFile } from "src/common/file/file.interface";

@Controller('/paycode')
export class PaycodeController {
  constructor(
    private readonly paycodeService: PaycodeService,
    private readonly paginationService: PaginationService,
  ) { }


  @ResponsePaging('paycode.list', {
    classSerialization: PaycodeListSerializtion
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      sort,
      search,
      availableSort,
      availableSearch,
    }: PaycodeListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = {
      ...search
    }

    const paycodes = await this.paycodeService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })
    const total = await this.paycodeService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: paycodes
    }
  }

  @Response('paycode.delete')
  @Delete('/delete/:id')
  async delete(@Param('id') id: string) {
    this.paycodeService.delete(id)
  }

  @Response('paycode.reset')
  @Put('/reset/:id')
  async reset(@Param('id') id: string) {
    this.paycodeService.reset(id)
  }

  @Response('paycode.create')
  @UploadFileSingle('image')
  @Post('/create')
  async create(@Body() data: any, @UploadedFile(QrcodeReadPipe) image: string) {
    const exists = await this.paycodeService.exists(data.price);
    if (exists) {
      throw new BadRequestException({
        statusCode: ENUM_PAYCODE_STATUS_CODE_ERROR.PAYCODE_EXIST_ERROR,
        message: 'paycode.error.exist',
      });
    }

    try {
      const create = await this.paycodeService.create({
        ...data,
        image
      });

      return {
        _id: create._id,
      };
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

}