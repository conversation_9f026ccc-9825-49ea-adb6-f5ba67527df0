import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { PaycodeDatabaseName, PaycodeEntity, PaycodeSchema } from "./schemas/paycode.schema";
import { PaycodeService } from "./services/paycode.service";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: PaycodeEntity.name,
          schema: PaycodeSchema,
          collection: PaycodeDatabaseName
        }
      ],
      DATABASE_CONNECTION_NAME
    )
  ],
  exports: [PaycodeService],
  providers: [PaycodeService],
  controllers: []
})
export class PaycodeModule { }