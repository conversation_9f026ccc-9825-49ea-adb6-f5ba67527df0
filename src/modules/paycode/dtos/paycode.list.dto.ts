import { PaginationListAbstract } from "src/common/pagination/abstracts/pagination.abstract";
import { PaginationAvailableSearch, PaginationAvailableSort, PaginationPage, PaginationPerPage, PaginationSearch, PaginationSort } from "src/common/pagination/decorators/pagination.decorator";
import { IPaginationSort } from "src/common/pagination/pagination.interface";
import { PAYCODE_DEFAULT_AVAILABLE_SEARCH, PAYCODE_DEFAULT_PAGE, PAYCODE_DEFAULT_PER_PAGE, PAYCODE_DEFAULT_SORT, PAYCODE_DEFAULT_AVAILABLE_SORT } from "../constants/paycode.list.constant";

export class PaycodeListDto implements PaginationListAbstract {
  @PaginationSearch(PAYCODE_DEFAULT_AVAILABLE_SEARCH)
  readonly search: Record<string, any>

  @PaginationAvailableSearch(PAYCODE_DEFAULT_AVAILABLE_SEARCH)
  readonly availableSearch: string[];

  @PaginationPage(PAYCODE_DEFAULT_PAGE)
  readonly current: number;

  @PaginationPerPage(PAYCODE_DEFAULT_PER_PAGE)
  readonly pageSize: number;

  @PaginationSort(PAYCODE_DEFAULT_SORT, PAYCODE_DEFAULT_AVAILABLE_SORT)
  readonly sort: IPaginationSort;

  @PaginationAvailableSort(PAYCODE_DEFAULT_AVAILABLE_SORT)
  readonly availableSort: string[];
}