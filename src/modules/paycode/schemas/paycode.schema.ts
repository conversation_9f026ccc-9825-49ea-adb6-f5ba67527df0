import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true, versionKey: false })
export class PaycodeEntity {
  @Prop({
    required: true,
  })
  days: number;

  @Prop({
    required: true,
  })
  price: number

  @Prop({
    required: true,
  })
  image: string;

  @Prop({
    default: true,
  })
  available: boolean
}

export const PaycodeDatabaseName = 'paycodes';
export const PaycodeSchema = SchemaFactory.createForClass(PaycodeEntity);

export type PaycodeDocument = PaycodeEntity & Document;
