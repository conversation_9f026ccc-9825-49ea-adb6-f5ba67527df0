import { Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { PaycodeCreateDto } from "../dtos/paycode.create.dto";
import { IPaycodeDocument } from "../paycode.interface";
import { PaycodeDocument, PaycodeEntity } from "../schemas/paycode.schema";

@Injectable()
export class PaycodeService {
  constructor(
    @DatabaseEntity(PaycodeEntity.name)
    private readonly paycodeModel: Model<PaycodeDocument>
  ) {
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IPaycodeDocument[]> {
    const paycodes = this.paycodeModel.find(find)
    if (
      options &&
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      paycodes.limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      paycodes.sort(options.sort)
    }

    return paycodes.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.paycodeModel.countDocuments(find)
  }

  async delete(id: string) {
    return this.paycodeModel.findByIdAndDelete(id)
  }

  async reset(id: string) {
    const paycode = await this.paycodeModel.findById(id)
    if (paycode) {
      paycode.available = true
      await paycode.save()
    }
  }

  async exists(price: number) {
    const exists = await this.paycodeModel.exists({
      price,
    });
    return !!exists;
  }

  async findByPrice(price: number) {
    const find = await this.paycodeModel.findOne({
      price,
    });
    return find;
  }

  async findAvailableCodeByDays(days: number) {
    const find = await this.paycodeModel.findOne({
      days,
      available: true
    }, null, {
      sort: { price: -1 }
    });
    return find;
  }

  async assignCode(days: number) {
    const code = await this.findAvailableCodeByDays(days)
    if (!code) return {
      price: -1,
      codeUrl: ''
    }
    code.available = false
    await code.save()
    return {
      price: code.price,
      codeUrl: code.image
    }
  }

  async releaseCode(price: number) {
    const code = await this.findByPrice(price)
    code.available = true
    await code.save()
    return code
  }

  async create(data: PaycodeCreateDto): Promise<PaycodeDocument> {
    const create: PaycodeDocument = new this.paycodeModel(data);
    return create.save();
  }

}