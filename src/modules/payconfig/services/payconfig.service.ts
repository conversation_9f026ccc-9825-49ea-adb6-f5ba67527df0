import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { IDatabaseFindAllOptions } from 'src/common/database/database.interface';
import { DatabaseEntity } from 'src/common/database/decorators/database.decorator';
import { PayconfigCreateDto } from '../dtos/payconfig.create.dto';
import { IPayconfigDocument } from '../payconfig.interface';
import {
  PayconfigDocument,
  PayconfigEntity,
} from '../schemas/payconfig.schema';

@Injectable()
export class PayconfigService {
  constructor(
    @DatabaseEntity(PayconfigEntity.name)
    private readonly payconfigModel: Model<PayconfigDocument>
  ) { }

  async findAvailable(): Promise<IPayconfigDocument[]> {
    const payconfigs = this.payconfigModel.find({
      disabled: false,
    });
    return payconfigs.lean();
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IPayconfigDocument[]> {
    const payconfigs = this.payconfigModel.find(find);
    if (options && options.limit !== undefined && options.skip !== undefined) {
      payconfigs.limit(options.limit).skip(options.skip);
    }

    if (options && options.sort) {
      payconfigs.sort(options.sort);
    }

    return payconfigs.lean();
  }

  async getTotal(find?: Record<string, any>) {
    return this.payconfigModel.countDocuments(find);
  }

  async delete(id: string) {
    return this.payconfigModel.findByIdAndDelete(id);
  }

  async exists(days: number) {
    const exists = await this.payconfigModel.exists({
      days,
    });
    return !!exists;
  }

  async create(data: PayconfigCreateDto): Promise<PayconfigDocument> {
    const create: PayconfigDocument = new this.payconfigModel(data);
    return create.save();
  }

  async dicreaseLimit(days: number) {
    const config = await this.payconfigModel.findOne({
      days,
      limit: {
        $gt: 0
      }
    });
    if (config) {
      config.limit -= 1
      await config.save()
    }
  }
}
