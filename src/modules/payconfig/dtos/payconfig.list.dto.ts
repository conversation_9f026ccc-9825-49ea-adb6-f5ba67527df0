import { PaginationListAbstract } from "src/common/pagination/abstracts/pagination.abstract";
import { PaginationAvailableSearch, PaginationAvailableSort, PaginationPage, PaginationPerPage, PaginationSearch, PaginationSort } from "src/common/pagination/decorators/pagination.decorator";
import { IPaginationSort } from "src/common/pagination/pagination.interface";
import { PAYCONFIG_DEFAULT_AVAILABLE_SEARCH, PAYCONFIG_DEFAULT_PAGE, PAYCONFIG_DEFAULT_PER_PAGE, PAYCONFIG_DEFAULT_SORT, PAYCONFIG_DEFAULT_AVAILABLE_SORT } from "../constants/payconfig.list.constant";

export class PayconfigListDto implements PaginationListAbstract {
  @PaginationSearch(PAYCONFIG_DEFAULT_AVAILABLE_SEARCH)
  readonly search: Record<string, any>

  @PaginationAvailableSearch(PAYCONFIG_DEFAULT_AVAILABLE_SEARCH)
  readonly availableSearch: string[];

  @PaginationPage(PAYCONFIG_DEFAULT_PAGE)
  readonly current: number;

  @PaginationPerPage(PAYCONFIG_DEFAULT_PER_PAGE)
  readonly pageSize: number;

  @PaginationSort(PAYCONFIG_DEFAULT_SORT, PAYCONFIG_DEFAULT_AVAILABLE_SORT)
  readonly sort: IPaginationSort;

  @PaginationAvailableSort(PAYCONFIG_DEFAULT_AVAILABLE_SORT)
  readonly availableSort: string[];
}