import { Controller, Get } from "@nestjs/common";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { ResponseList } from "src/common/response/decorators/response.decorator";
import { IResponseList } from "src/common/response/response.interface";
import { PayconfigListAvailableSerialization } from "../serializations/payconfig.list.available.serialization";
import { PayconfigService } from "../services/payconfig.service";

@Controller('/payconfig')
export class PayconfigPublicController {
  constructor(
    private readonly payconfigService: PayconfigService,
    private readonly paginationService: PaginationService,
  ) { }


  @ResponseList('payconfig.list', {
    classSerialization: PayconfigListAvailableSerialization
  })
  @Get('/list')
  async listAvailable(): Promise<IResponseList> {
    const data = await this.payconfigService.findAvailable()
    return {
      data
    }
  }

}