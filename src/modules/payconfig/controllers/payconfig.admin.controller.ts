import {
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Body,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { AuthAdminJwtGuard, AuthJwtGuard } from 'src/common/auth/decorators/auth.jwt.decorator';
import { ENUM_ERROR_STATUS_CODE_ERROR } from 'src/common/error/constants/error.status-code.constant';
import { PaginationService } from 'src/common/pagination/services/pagination.service';
import {
  Response,
  ResponsePaging,
} from 'src/common/response/decorators/response.decorator';
import { IResponsePaging } from 'src/common/response/response.interface';
import { ENUM_PAYCONFIG_STATUS_CODE_ERROR } from '../constants/payconfig.status-code.constant';
import { PayconfigCreateDto } from '../dtos/payconfig.create.dto';
import { PayconfigListDto } from '../dtos/payconfig.list.dto';
import { PayconfigListSerializtion } from '../serializations/payconfig.list.serialization';
import { PayconfigService } from '../services/payconfig.service';

@AuthAdminJwtGuard()
@Controller('/payconfig')
export class PayconfigController {
  constructor(
    private readonly payconfigService: PayconfigService,
    private readonly paginationService: PaginationService
  ) { }

  @ResponsePaging('payconfig.list', {
    classSerialization: PayconfigListSerializtion,
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      sort,
      search,
      availableSort,
      availableSearch,
    }: PayconfigListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize);
    const find: Record<string, any> = {
      ...search,
    };

    const payconfigs = await this.payconfigService.findAll(find, {
      limit: pageSize,
      skip,
      sort,
    });
    const total = await this.payconfigService.getTotal(find);
    const totalPage = await this.paginationService.totalPage(total, pageSize);
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: payconfigs,
    };
  }

  @Response('payconfig.delete')
  @Delete('/delete/:id')
  async delete(@Param('id') id: string) {
    this.payconfigService.delete(id);
  }

  @Response('payconfig.create')
  @Post('/create')
  async add(@Body() data: PayconfigCreateDto) {
    const exists = await this.payconfigService.exists(data.days);
    if (exists) {
      throw new BadRequestException({
        statusCode: ENUM_PAYCONFIG_STATUS_CODE_ERROR.PAYCONFIG_EXIST_ERROR,
        message: 'payconfig.error.exist',
      });
    }

    try {
      const create = await this.payconfigService.create(data);

      return {
        _id: create._id,
      };
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }
}
