import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { PayconfigDatabaseName, PayconfigEntity, PayconfigSchema } from "./schemas/payconfig.schema";
import { PayconfigService } from "./services/payconfig.service";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: PayconfigEntity.name,
          schema: PayconfigSchema,
          collection: PayconfigDatabaseName
        }
      ],
      DATABASE_CONNECTION_NAME
    )
  ],
  exports: [PayconfigService],
  providers: [PayconfigService],
  controllers: []
})
export class PayconfigModule { }