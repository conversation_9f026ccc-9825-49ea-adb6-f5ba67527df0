import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true, versionKey: false })
export class PayconfigEntity {
  @Prop({
    required: true,
  })
  days: number;

  @Prop({
    required: true,
  })
  price: number

  @Prop({
    required: true,
  })
  desc: string;

  @Prop({
    default: -1
  })
  limit: number

  @Prop({
    default: true
  })
  disabled: boolean
}

export const PayconfigDatabaseName = 'payconfigs';
export const PayconfigSchema = SchemaFactory.createForClass(PayconfigEntity);

export type PayconfigDocument = PayconfigEntity & Document;
