import { Injectable } from "@nestjs/common";
import { spawn } from "child_process";
import { mkdirpSync } from "fs-extra";
import { tmpdir } from "os";
import { join } from "path";
import { v4 } from "uuid";
import AdmZip from "adm-zip";

@Injectable()
export class CAService {
  private readonly KEY_NAME = 'ultraCA'
  private readonly CN_CONTENT = 'KRAKEN'

  async genKey(): Promise<string> {
    return new Promise((resolve) => {
      const tempDir = join(tmpdir(), v4())
      mkdirpSync(tempDir)

      const paths = {
        keyPath: join(tempDir, `${this.KEY_NAME}.key`),
        pemPath: join(tempDir, `${this.KEY_NAME}.pem`),
        crtPath: join(tempDir, `${this.KEY_NAME}.crt`),
        zipPath: join(tempDir, `${this.KEY_NAME}.zip`),
      }

      const subjectContent = `/CN=${this.CN_CONTENT}`
      let processHandle = spawn('openssl', `req -x509 -new -nodes -keyout ${paths.keyPath} -sha256 -days 365 -out ${paths.pemPath} -subj ${subjectContent}`.split(' '))
      processHandle.stderr.on('data', d => console.log(d.toString()))
      processHandle.stdout.on('data', d => console.log(d.toString()))
      processHandle.on('exit', () => {
        processHandle = spawn('openssl', `x509 -outform der -in ${paths.pemPath} -out ${paths.crtPath}`.split(' '))
        processHandle.stderr.on('data', d => console.log(d.toString()))
        processHandle.stdout.on('data', d => console.log(d.toString()))
        processHandle.on('exit', () => {
          const zip = new AdmZip()
          zip.addLocalFile(paths.keyPath)
          zip.addLocalFile(paths.pemPath)
          zip.addLocalFile(paths.crtPath)

          zip.writeZip(paths.zipPath)
          resolve(paths.zipPath)
        })
      })
    })
  }
}
