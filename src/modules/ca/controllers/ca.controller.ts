import { Controller, Get, StreamableFile } from "@nestjs/common";
import { createReadStream, unlink } from "fs";
import { CAService } from "../services/ca.service";

@Controller('ca')
export class CAController {
  constructor(
    private caService: CAService
  ) { }
  @Get()
  async getCA() {
    const zipPath = await this.caService.genKey()
    const file = createReadStream(zipPath)
    file.on('end', () => {
      unlink(zipPath, () => { })
    })
    return new StreamableFile(file)

    // res.download(zipPath)
    // console.log(`del path: `, dirname(zipPath))
    // del(dirname(zipPath), {
    //   force: true
    // })    

  }
}