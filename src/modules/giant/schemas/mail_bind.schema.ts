import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MSchema, Types } from 'mongoose';
import { v4 } from "uuid";
import { GiantEntity } from './giant.schema';

@Schema({ timestamps: true, versionKey: false })
export class MailBindEntity {
  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: GiantEntity.name,
  })
  giant: Types.ObjectId;

  @Prop({
    required: true,
    type: String
  })
  mail: string

  @Prop({
    required: true,
    type: String
  })
  status: string
}

export const MailBindDatabaseName = 'mail_binds';
export const MailBindSchema = SchemaFactory.createForClass(MailBindEntity);

export type MailBindDocument = MailBindEntity & Document;
