import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MSchema, Types } from 'mongoose';
import { v4 } from "uuid";
import { GiantEntity } from './giant.schema';

@Schema({ timestamps: true, versionKey: false })
export class MailFindEntity {
  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: GiantEntity.name,
  })
  giant: Types.ObjectId;

  @Prop({
    required: true,
    type: String
  })
  mail: string

  @Prop({
    required: true,
    type: String
  })
  status: string
}

export const MailFindDatabaseName = 'mail_finds';
export const MailFindSchema = SchemaFactory.createForClass(MailFindEntity);

export type MailFindDocument = MailFindEntity & Document;
