import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>hemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MSchema } from 'mongoose';
import { v4 } from "uuid";
import { randomBytes } from "crypto";

@Schema()
export class Machine {
  @Prop({
    required: true
  })
  id: string

  @Prop({
    default: 'Anonymous'
  })
  name: string
}

const machineSchema = SchemaFactory.createForClass(Machine);

@Schema()
export class SignRSA {
  @Prop({
    default: ''
  })
  publicKey: string

  @Prop({
    default: ''
  })
  privateKey: string
}

@Schema({ timestamps: true, versionKey: false })
export class GiantEntity {
  @Prop({
    default: () => v4()
  })
  key: string;

  @Prop({
    default: () => v4(),
  })
  inviteCode: string;

  @Prop({
    default: '',
  })
  inviteeCode: string;

  @Prop({
    default: false,
  })
  use30: boolean;

  @Prop({
    default: false,
  })
  use365: boolean;

  @Prop({
    default: 0,
  })
  inviteCount: number;

  @Prop({
    default: () => new Date()
  })
  activeTime: Date;

  @Prop({
    required: true,
  })
  days: number;

  @Prop({
    default: false
  })
  offline: boolean;

  @Prop({
    default: false
  })
  disabled: boolean;

  @Prop({
    default: ''
  })
  mail: string

  @Prop({
    type: [machineSchema],
    default: () => []
  })
  machines: Machine[]


  // define a binary with 32bytes length, and set random default 
  @Prop({
    type: Buffer,
    default: () => randomBytes(32)
  })
  encKey: Buffer;

  @Prop({
    type: Buffer,
    default: () => randomBytes(16)
  })
  encIv: Buffer

  @Prop({
    type: SignRSA,
    default: () => null
  })
  signRSA: SignRSA

  @Prop({
    type: String,
  })
  developerId: string
}

export const GiantDatabaseName = 'giants';
export const GiantSchema = SchemaFactory.createForClass(GiantEntity);

export type GiantDocument = GiantEntity & Document;
