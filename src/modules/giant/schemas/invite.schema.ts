import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MSchema, Types } from 'mongoose';
import { v4 } from "uuid";
import { GiantEntity } from './giant.schema';

@Schema({ timestamps: true, versionKey: false })
export class InviteEntity {
  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: GiantEntity.name,
  })
  inviter: Types.ObjectId;

  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: GiantEntity.name,
  })
  invitee: Types.ObjectId;

  @Prop({
    required: true,
    type: String
  })
  ip: string
}

export const InviteDatabaseName = 'invites';
export const InviteSchema = SchemaFactory.createForClass(InviteEntity);

export type InviteDocument = InviteEntity & Document;
