import { <PERSON><PERSON><PERSON><PERSON>, IsBoolean, IsDate, IsDateString, IsN<PERSON>ber, IsOptional, IsString } from "class-validator";
import { StringOrNumber } from "src/common/request/validations/request.string-or-number-or-boolean.validation";

export class GiantCreateDto {
  @IsString()
  @IsOptional()
  readonly key: string

  @IsString()
  @IsOptional()
  readonly inviteCode: string

  @IsString()
  @IsOptional()
  readonly inviteeCode: string;

  @IsBoolean()
  @IsOptional()
  readonly use30: boolean;

  @IsBoolean()
  @IsOptional()
  readonly use365: boolean;

  @IsNumber()
  @IsOptional()
  readonly inviteCount: number;

  @IsString()
  @IsOptional()
  readonly mail: string;

  @IsArray()
  @IsOptional()
  readonly machines: {
    id: string
    name: string
  }[];

  @IsDateString()
  @IsOptional()
  readonly activeTime: Date;

  @IsNumber()
  readonly days: number;
}