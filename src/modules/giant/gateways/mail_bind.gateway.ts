import { OnGatewayDisconnect, SubscribeMessage, WebSocketGateway, WebSocketServer } from "@nestjs/websockets";

@WebSocketGateway({
  path: '/ws/mail/bind',
})
export class MailBindGateway implements OnGatewayDisconnect {
  @WebSocketServer() private server: any;

  private readonly mailBindMap = new Map<string, {
    client: any,
  }>()

  revoke(bindId: string) {
    const info = this.mailBindMap.get(bindId)
    if (!info) return
    info.client.send(JSON.stringify({
      event: 'binded',
    }))
    info.client.close()
  }

  @SubscribeMessage('bind')
  async waitFor(client: any, bindId: string) {
    this.mailBindMap.set(bindId, { client })
    setTimeout(() => {
      client.close()
    }, 60 * 60 * 1000)
  }

  handleDisconnect(client: any) {
    // find pay map whose client is current
    console.log('disconnect mail bind gateway')
    const info = Array.from(this.mailBindMap.entries()).find(([_, v]) => v.client === client)
    if (info) {
      console.log('clear...')
      this.mailBindMap.delete(info[0])
    }

  }

}