import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { differenceInDays } from "date-fns";
import { FilterQuery, Model } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { HelperDateService } from "src/common/helper/services/helper.date.service";
import { MailService } from "src/common/mail/services/mail.service";
import { ENUM_GIANT_MAIL_STATUS_CODE_ERROR } from "../constants/mail_bind.status-code.constant";
import { GiantCreateDto } from "../dtos/giant.create.dto";
import { GiantUpdateDto } from "../dtos/giant.update.dto";
import { IGiantDocument } from "../giant.interface";
import { IMailBindDocument } from "../mail_bind.interface";
import { GiantDocument, GiantEntity, Machine, SignRSA } from "../schemas/giant.schema";
import { MailFindEntity } from "../schemas/mail_find.schema";
import { createCipheriv, createDecipheriv, createSign, createVerify, generateKeyPairSync, randomBytes } from "crypto";
import { RecordService } from "src/modules/record/services/record.service";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class GiantService {
  private env: string

  constructor(
    @DatabaseEntity(GiantEntity.name)
    private readonly giantModel: Model<GiantDocument>,
    @DatabaseEntity(MailFindEntity.name)
    private readonly mailFindModel: Model<MailFindEntity>,
    private readonly recordService: RecordService,
    private readonly helperDateService: HelperDateService,
    private readonly configService: ConfigService,
  ) {
    this.env = configService.get<string>('app.env')
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IGiantDocument[]> {
    const giants = this.giantModel.find(find)
    if (
      options &&
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      giants.limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      giants.sort(options.sort)
    }

    return giants.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.giantModel.countDocuments(find)
  }

  async findByKey(key: string) {
    const giant = await this.giantModel.findOne({
      key,
    });
    return giant;
  }

  async findByMail(mail: string) {
    if (!mail) return null
    const giant = await this.giantModel.findOne({
      mail,
    });
    return giant;
  }

  async findByInviteCode(inviteCode: string) {
    const giant = await this.giantModel.findOne({
      inviteCode,
    });
    return giant;
  }

  async findOne(filterQuery: FilterQuery<GiantDocument>) {
    const giant = await this.giantModel.findOne(filterQuery);
    return giant;
  }

  async exists(key: string) {
    const exists = await this.giantModel.exists({
      key,
    });
    return !!exists;
  }

  async existsMail(mail: string) {
    const exists = await this.giantModel.exists({
      mail,
    });
    return !!exists;
  }

  async create(data: GiantCreateDto) {
    const create: GiantDocument = new this.giantModel(data);
    return create.save();
  }

  async deleteById(id: string) {
    return this.giantModel.findByIdAndDelete(id)
  }

  async increment(giant: GiantDocument, machine: Machine) {
    giant.machines.push(machine)
    await giant.save()
  }

  async updateMachine(giant: GiantDocument, machine: Machine) {
    const machineIndex = giant.machines.findIndex(m => m.id === machine.id)
    if (machineIndex >= 0) {
      giant.machines.splice(machineIndex, 1, machine)
      await giant.save()
    }
  }

  async reset(id: string) {
    const doc = await this.giantModel.findById(id)
    if (doc) {
      doc.machines = []
      return doc.save()
    }
  }

  async resetByMail(id: string) {
    const doc = await this.mailFindModel.findById(id).populate('giant')
    const giant = await this.giantModel.findById(doc.giant)
    if (giant) {
      giant.machines = []
      return giant.save()
    }
  }

  async freeze(id: string, status: boolean) {
    const doc = await this.giantModel.findById(id)
    if (doc) {
      doc.disabled = status
      return doc.save()
    }
  }

  async unbindMail(id: string) {
    const doc = await this.giantModel.findById(id)
    if (doc) {
      doc.mail = ''
      return doc.save()
    }
  }

  async updateOneById(_id: string, giantUpdateDto: GiantUpdateDto, timezone: string) {
    const update: GiantDocument = await this.giantModel.findById(_id);
    update.activeTime = this.helperDateService.create({
      date: giantUpdateDto.activeTime,
      timezone
    })
    update.days = giantUpdateDto.days || update.days
    return update.save()
  }

  async createOrUpdateGiant(key: string, days: number) {
    let giant = await this.findByKey(key)
    if (giant) {
      this.addDaysToGiant(giant, days)
      await giant.save()

      this.recordService.createGiantRecord({
        type: 'giant',
        key: giant.key,
        giant_days: days,
        new_giant: false,
      }).catch(e => console.error(e))
      return giant
    }
    giant = new this.giantModel({ days })
    if (days === 30) {
      giant.use30 = true
    } else if (days === 365) {
      giant.use365 = true
    }
    await giant.save()

    this.recordService.createGiantRecord({
      type: 'giant',
      key: giant.key,
      giant_days: days,
      new_giant: true,
    }).catch(e => console.error(e))

    return giant
  }

  async bindMail(bindInfo: IMailBindDocument) {
    const giant = await this.giantModel.findById(bindInfo.giant)
    if (!giant) {
      throw new InternalServerErrorException({
        statusCode: ENUM_GIANT_MAIL_STATUS_CODE_ERROR.MAIL_FIND_NO_EXIST_ERROR,
        message: 'http.serverError.internalServerError',
        error: 'Giant信息不存在',
      })
    }
    giant.mail = bindInfo.mail
    await giant.save()
  }

  addDaysToGiant(giant: GiantDocument, days: number) {
    if (days < 0 || giant.days < 0) {
      giant.days = -1
    } else {
      const tryDays = differenceInDays(Date.now(), giant.activeTime)
      if (tryDays > giant.days) {
        giant.days = days
        giant.activeTime = new Date()
      } else {
        giant.days += days
      }
    }
  }

  getRemainDays(giant: GiantDocument) {
    if (giant.days < 0) {
      return 3600000
    }
    const tryDays = differenceInDays(Date.now(), giant.activeTime)
    return giant.days - tryDays
  }

  async encryptKrk(giant: GiantDocument, plain: string | Buffer): Promise<Buffer> {
    plain = typeof plain === 'string' ? Buffer.from(plain, 'base64') : plain

    if (!giant.encKey) {
      giant.encKey = randomBytes(32)
    }
    if (!giant.encIv) {
      giant.encIv = randomBytes(16)
    }

    await giant.save()

    const algorithm = 'aes-256-cbc';
    const key = giant.encKey
    const iv = giant.encIv

    const cipher = createCipheriv(algorithm, key, iv);
    const encrypted = Buffer.concat([cipher.update(plain), cipher.final()]);

    return encrypted;
  }

  async decryptKrk(giant: GiantDocument, cipher: string | Buffer): Promise<Buffer> {
    cipher = typeof cipher === 'string' ? Buffer.from(cipher, 'base64') : cipher

    const algorithm = 'aes-256-cbc';
    const key = giant.encKey;
    const iv = giant.encIv;

    const decipher = createDecipheriv(algorithm, key, iv);
    const decrypted = Buffer.concat([decipher.update(cipher), decipher.final()]);

    return decrypted;
  }

  async decryptKrkPublic(gid: string, cipher: string | Buffer): Promise<Buffer> {
    cipher = typeof cipher === 'string' ? Buffer.from(cipher, 'base64') : cipher

    const giant = await this.giantModel.findById(gid)
    if (!giant) {
      throw new InternalServerErrorException({
        statusCode: ENUM_GIANT_MAIL_STATUS_CODE_ERROR.MAIL_FIND_NO_EXIST_ERROR,
        message: 'http.serverError.internalServerError',
        error: 'failed',
      })
    }

    const algorithm = 'aes-256-cbc';
    const key = giant.encKey;
    const iv = giant.encIv;

    const decipher = createDecipheriv(algorithm, key, iv);
    const decrypted = Buffer.concat([decipher.update(cipher), decipher.final()]);

    return decrypted;
  }

  async signKrk(giant: GiantDocument, buffer: string | Buffer) {
    buffer = typeof buffer === 'string' ? Buffer.from(buffer, 'base64') : buffer
    if (!giant.signRSA) {
      giant.signRSA = this.generateKeys()
    }
    await giant.save()

    const sign = createSign('RSA-SHA256');
    sign.update(buffer);
    const signature = sign.sign(giant.signRSA.privateKey, 'hex');
    return { sign: signature, publicKey: giant.signRSA.publicKey }
  }

  async verifyKrk(giant: GiantDocument, buffer: string | Buffer, signature: string) {
    buffer = typeof buffer === 'string' ? Buffer.from(buffer, 'base64') : buffer;
    if (!giant.signRSA) {
      throw new Error('No RSA keys found on giant');
    }

    const verify = createVerify('RSA-SHA256');
    verify.update(buffer);
    const isValid = verify.verify(giant.signRSA.publicKey, signature, 'hex');
    return isValid;
  }

  generateKeys(): SignRSA {
    const { publicKey, privateKey } = generateKeyPairSync('rsa', {
      modulusLength: 2048,  // the length of your key in bits
      publicKeyEncoding: {
        type: 'spki',       // 'SubjectPublicKeyInfo' format
        format: 'pem'       // 'PEM' format
      },
      privateKeyEncoding: {
        type: 'pkcs8',      // 'Private Key Cryptography Standards 8' format
        format: 'pem',      // 'PEM' format
      }
    });

    return { publicKey, privateKey };
  }

  async verifyDeveloper(key: string) {
    if (this.env === 'development') {
      return {
        status: 'success',
        developerId: 'developer'
      }
    }

    const giant = await this.findByKey(key)
    if (giant) {
      if (giant.developerId) {
        return {
          status: 'success',
          developerId: giant.developerId
        }
      }
      return {
        status: 'require-activation'
      }
    }
    return {
      status: false
    }
  }

  async verifyKeyAndDeveloperId(key: string, developerId: string) {
    const giant = await this.giantModel.findOne({
      key,
      developerId
    })

    return !!giant
  }

  async verifyForce(machineId: string, giant?: GiantDocument) {
    // verify giant
    if (giant && this.getRemainDays(giant) > 0) {
      return true
    }

    return this.recordService.verifyForce(machineId)
  }

}