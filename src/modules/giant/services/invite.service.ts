import { Injectable } from "@nestjs/common";
import { Model } from "mongoose";
import { IDatabaseFindAllOptions } from "src/common/database/database.interface";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { HelperDateService } from "src/common/helper/services/helper.date.service";
import { InviteCreateDto } from "../dtos/invite.create.dto";
import { IInviteDocument } from "../invite.interface";
import { InviteDocument, InviteEntity } from "../schemas/invite.schema";

@Injectable()
export class InviteService {
  constructor(
    @DatabaseEntity(InviteEntity.name)
    private readonly inviteModel: Model<InviteDocument>,
    private readonly helperDateService: HelperDateService
  ) {
  }

  async findAll(
    find?: Record<string, any>,
    options?: IDatabaseFindAllOptions
  ): Promise<IInviteDocument[]> {
    const invites = this.inviteModel.find(find)
    if (
      options &&
      options.limit !== undefined &&
      options.skip !== undefined
    ) {
      invites.limit(options.limit).skip(options.skip)
    }

    if (options && options.sort) {
      invites.sort(options.sort)
    }

    return invites.lean()
  }

  async getTotal(find?: Record<string, any>) {
    return this.inviteModel.countDocuments(find)
  }

  async hasMoreIp(ip: string) {
    const invites = await this.inviteModel.find({
      ip
    })
    return invites.length > 1
  }

  async create(data: InviteCreateDto) {
    const create: InviteDocument = new this.inviteModel(data);
    return create.save();
  }
}