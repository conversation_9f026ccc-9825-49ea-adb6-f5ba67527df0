import { Injectable } from "@nestjs/common";
import { subHours } from "date-fns";
import { Model } from "mongoose";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { HelperDateService } from "src/common/helper/services/helper.date.service";
import { MailService } from "src/common/mail/services/mail.service";
import { GiantDocument } from "../schemas/giant.schema";
import { MailFindDocument, MailFindEntity } from "../schemas/mail_find.schema";

@Injectable()
export class MailFindService {
  constructor(
    @DatabaseEntity(MailFindEntity.name)
    private readonly mailFindModel: Model<MailFindDocument>,
    private readonly helperDateService: HelperDateService,
    private readonly mailService: MailService,

  ) {
  }

  async findById(id: string) {
    return this.mailFindModel.findById(id)
  }

  async deleteById(id: string) {
    return this.mailFindModel.findByIdAndDelete(id)
  }

  async removeInvalidFind() {
    return this.mailFindModel.deleteMany({
      createdAt: {
        $lt: subHours(new Date(), 24)
      }
    })
  }

  async create(giant: GiantDocument, mail: string, host: string) {
    const finded = await this.mailFindModel.findOne({
      giant: giant._id,
    })

    if (finded?.status === 'sending' || finded?.status === 'sended') {
      return {
        status: finded.status,
        find: finded
      }
    }

    const find = await this.mailFindModel.findOneAndUpdate({
      giant: giant._id,
    }, {
      giant: giant._id,
      mail,
      status: 'sending'
    }, {
      new: true,
      upsert: true
    })

    await find.save()

    let mailSending = true
    setTimeout(() => {
      if (mailSending) {
        find.status = 'timeout'
        find.save()
      }
    }, 60 * 10 * 1000)

    this.mailService.sendToMail(
      mail,
      'Giant密钥找回',
      `<p>您绑定的密钥是：<strong>${giant.key}</strong></p>
      <br />
      <p>密钥激活流程： 点击软件右上角 "K" --> 点击升级至Giant版本 --> 输入密钥激活。<p>
      <p>该密钥作用于两台设备，若超出限制，将会激活失败，请点击以下链接重置设备：<p>
      <p><a href="http://${host}/api/v1/giant/mail/reset/${find._id}">http://${host}/api/v1/giant/mail/reset/${find._id}</a>(此链接24小时内有效)</p>
      `
    )
      .then(() => {
        find.status = 'sended'
      })
      .catch(() => {
        find.status = 'error'
      })
      .finally(() => {
        find.save().then(() => {
          mailSending = false
        })
      })
    return {
      status: 'new',
      find
    }
  }
}