import { Injectable } from "@nestjs/common";
import { subHours } from "date-fns";
import { Model } from "mongoose";
import { DatabaseEntity } from "src/common/database/decorators/database.decorator";
import { HelperDateService } from "src/common/helper/services/helper.date.service";
import { MailService } from "src/common/mail/services/mail.service";
import { GiantDocument } from "../schemas/giant.schema";
import { MailBindDocument, MailBindEntity } from "../schemas/mail_bind.schema";

@Injectable()
export class MailBindService {
  constructor(
    @DatabaseEntity(MailBindEntity.name)
    private readonly mailBindModel: Model<MailBindDocument>,
    private readonly helperDateService: HelperDateService,
    private readonly mailService: MailService,

  ) {
  }

  async findById(id: string) {
    return this.mailBindModel.findById(id)
  }

  async deleteById(id: string) {
    return this.mailBindModel.findByIdAndDelete(id)
  }

  async removeInvalidBind() {
    return this.mailBindModel.deleteMany({
      createdAt: {
        $lt: subHours(new Date(), 24)
      }
    })
  }

  async create(giant: GiantDocument, mail: string, host: string) {
    const binded = await this.mailBindModel.findOne({
      giant: giant._id,
      mail
    })

    if (binded?.status === 'sending' || binded?.status === 'sended') {
      return {
        status: binded.status,
        bind: binded
      }
    }

    const bind = await this.mailBindModel.findOneAndUpdate({
      giant: giant._id,
    }, {
      giant: giant._id,
      mail,
      status: 'sending'
    }, {
      new: true,
      upsert: true
    })

    await bind.save()

    let mailSending = true
    setTimeout(() => {
      if (mailSending) {
        bind.status = 'timeout'
        bind.save()
      }
    }, 60 * 10 * 1000)

    this.mailService.sendToMail(
      mail,
      '确认绑定邮箱，勿回',
      `
      <h1>绑定邮箱</h1>
      <h3>请点击以下链接确认绑定邮箱 （该链接将在1-2天内失效）： </h3>
      <p>
        <a href="http://${host}/api/v1/giant/mail/confirm/${bind._id}">http://${host}/api/v1/giant/mail/confirm/${bind._id}</a>
      </p>
      `
    )
      .then(() => {
        bind.status = 'sended'
      })
      .catch(() => {
        bind.status = 'error'
      })
      .finally(() => {
        bind.save().then(() => {
          mailSending = false
        })
      })
    return {
      status: 'new',
      bind
    }
  }
}