import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { DATABASE_CONNECTION_NAME } from "src/common/database/constants/database.constant";
import { MailModule } from "src/common/mail/mail.module";
import { MailBindGateway } from "./gateways/mail_bind.gateway";
import { GiantDatabaseName, GiantEntity, GiantSchema } from "./schemas/giant.schema";
import { InviteDatabaseName, InviteEntity, InviteSchema } from "./schemas/invite.schema";
import { MailBindDatabaseName, MailBindEntity, MailBindSchema } from "./schemas/mail_bind.schema";
import { MailFindEntity, MailFindSchema, MailFindDatabaseName } from "./schemas/mail_find.schema";
import { GiantService } from "./services/giant.service";
import { InviteService } from "./services/invite.service";
import { MailBindService } from "./services/mail_bind.service";
import { MailFindService } from "./services/mail_find.service";
import { RecordModule } from "../record/record.module";

@Module({
  imports: [
    MailModule,
    RecordModule,
    MongooseModule.forFeature(
      [
        {
          name: GiantEntity.name,
          schema: GiantSchema,
          collection: GiantDatabaseName
        },
        {
          name: InviteEntity.name,
          schema: InviteSchema,
          collection: InviteDatabaseName
        },
        {
          name: MailBindEntity.name,
          schema: MailBindSchema,
          collection: MailBindDatabaseName
        },
        {
          name: MailFindEntity.name,
          schema: MailFindSchema,
          collection: MailFindDatabaseName
        },
      ],
      DATABASE_CONNECTION_NAME
    )
  ],
  exports: [GiantService, InviteService, MailBindService, MailFindService, MailBindGateway],
  providers: [GiantService, InviteService, MailBindService, MailFindService, MailBindGateway],
  controllers: []
})
export class GiantModule { }