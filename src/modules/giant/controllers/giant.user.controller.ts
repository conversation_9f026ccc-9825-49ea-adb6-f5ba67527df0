import { Body, Controller, Get, Headers, InternalServerErrorException, Param, Post, Request } from "@nestjs/common";
import { intersection } from "lodash";
import { MailService } from "src/common/mail/services/mail.service";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { RequestIp } from "src/common/request/decorators/request.decorator";
import { Response, ResponseTimeout } from "src/common/response/decorators/response.decorator";
import { ENUM_INVITE_STATUS_CODE_ERROR } from "../constants/invite.status-code.constant";
import { ENUM_GIANT_MAIL_STATUS_CODE_ERROR } from "../constants/mail_bind.status-code.constant";
import { AuthGiant, AuthGiantWeak, GetGiant, GetGiantError, GetMachineId, OptionalGiant } from "../decorators/giant.decorator";
import { GiantCreateDto } from "../dtos/giant.create.dto";
import { GiantInviteDto } from "../dtos/giant.invite.dto";
import { MailBindGateway } from "../gateways/mail_bind.gateway";
import { GiantDocument } from "../schemas/giant.schema";
import { GiantService } from "../services/giant.service";
import { InviteService } from "../services/invite.service";
import { MailBindService } from "../services/mail_bind.service";
import { MailFindService } from "../services/mail_find.service";

@Controller('/giant')
export class GiantController {
  constructor(
    private readonly giantService: GiantService,
    private readonly inviteService: InviteService,
    private readonly mailBindService: MailBindService,
    private readonly mailFindService: MailFindService,
    private readonly mailBindGateway: MailBindGateway,
    private readonly paginationService: PaginationService,
  ) { }

  @Response('giant.get')
  @AuthGiant()
  @Get('/get')
  async getGiant(
    @GetGiant() giant: GiantDocument
  ) {
    return {
      ...giant.toObject(),
      activeTime: giant.activeTime.getTime()
    }
  }

  @Response('giant.getWeak')
  @AuthGiantWeak()
  @Get('/getWeak')
  async getGiantWeak(
    @GetGiant() giant: GiantDocument,
    @GetGiantError() errorCode: number,
  ) {

    if (errorCode) {
      throw new InternalServerErrorException({
        statusCode: errorCode,
        message: 'http.serverError.internalServerError',
        error: '验证失败',
      })
    }

    return {
      ...giant.toObject(),
      activeTime: giant.activeTime.getTime()
    }
  }

  @Response('giant.mail.bind')
  @AuthGiant()
  @ResponseTimeout('600s')
  @Post('/mail/bind')
  async mailBind(
    @GetGiant() giant: GiantDocument,
    @Headers('host') host: string,
    @Body('mail') mail: string
  ) {
    if (giant.mail) {
      return {
        msg: '密钥已绑定邮箱',
        binded: true
      }
    }
    const existsMail = await this.giantService.existsMail(mail)
    if (existsMail) {
      throw new InternalServerErrorException({
        statusCode: ENUM_GIANT_MAIL_STATUS_CODE_ERROR.MAIL_BIND_ALREADY_EXIST_ERROR,
        message: 'http.serverError.internalServerError',
        error: '该邮箱已被绑定',
      })
    }

    const res = await this.mailBindService.create(giant, mail, host)
    if (res.status === 'new') {
      return {
        msg: '绑定成功, 10分钟之内发送邮件至该邮箱，请确认邮件，若未收到，请重新绑定。',
        bindId: res.bind._id
      }
    } else if (res.status === 'sended') {
      return {
        msg: '邮件已发送',
        bindId: res.bind._id
      }
    }

    return {
      msg: '邮件正在发送中',
      bindId: res.bind._id
    }
  }

  @Get('/mail/confirm/:id')
  async mailConfirm(
    @Param('id') id: string
  ) {
    const bindInfo = await this.mailBindService.findById(id)
    if (bindInfo) {
      await this.giantService.bindMail(bindInfo)
      await this.mailBindService.deleteById(id)
      this.mailBindGateway.revoke(id)
      return '绑定成功'
    }
    return '链接已失效'
  }

  @Response('giant.mail.find')
  @Post('/mail/find')
  async mailFind(
    @Body('mail') mail: string,
    @Headers('host') host: string,
  ) {
    const giant = await this.giantService.findByMail(mail)
    if (!giant) {
      throw new InternalServerErrorException({
        statusCode: ENUM_GIANT_MAIL_STATUS_CODE_ERROR.MAIL_FIND_NO_EXIST_ERROR,
        message: 'http.serverError.internalServerError',
        error: 'Giant信息不存在',
      })
    }
    const res = await this.mailFindService.create(giant, mail, host)
    if (res.status === 'new') {
      return {
        msg: '密钥发送中，将在10分钟之内到达您的邮箱'
      }
    } else if (res.status === 'sended') {
      return {
        msg: '邮件已发送'
      }
    }

    return {
      msg: '邮件正在发送中'
    }
  }

  @Response('giant.machine.release')
  @AuthGiant()
  @Post('/machine/release')
  async releaseMachine(
    @GetGiant() giant: GiantDocument,
    @Body('machineId') machineId: string
  ) {
    const machineIndex = giant.machines.findIndex(m => m.id === machineId)
    if (machineIndex >= 0) {
      giant.machines.splice(machineIndex, 1)
      console.log(giant)
      await giant.save()
    }
    return {
      msg: '释放成功'
    }
  }

  @Get('/mail/reset/:id')
  async reset(
    @Param('id') id: string
  ) {
    try {
      await this.giantService.resetByMail(id);
      return '重置成功'
    } catch (err: any) {
      // throw new InternalServerErrorException({
      //   statusCode: ENUM_GIANT_MAIL_STATUS_CODE_ERROR.MAIL_GIANT_RESET_ERROR,
      //   message: 'http.serverError.internalServerError',
      //   error: err.message,
      // });
    }
    return '链接已失效'
  }

  @Response('giant.invitee')
  @AuthGiant()
  @Post('/invitee')
  async invitee(
    // 被邀请人
    @GetGiant() giant: GiantDocument,
    @GetMachineId() machineId: string,
    @Body() giantInviteDto: GiantInviteDto,
    @RequestIp() ip: string,
  ) {
    if (giant.inviteeCode) {
      throw new InternalServerErrorException({
        statusCode: ENUM_INVITE_STATUS_CODE_ERROR.INVITE_ALREADY_INVITED_ERROR,
        message: 'http.serverError.internalServerError',
        error: '邀请码已填写',
      })
    }
    if (giant.inviteeCode === giant.inviteCode) {
      throw new InternalServerErrorException({
        statusCode: ENUM_INVITE_STATUS_CODE_ERROR.INVITE_SELF_ERROR,
        message: 'http.serverError.internalServerError',
        error: '不可填写自己的邀请码',
      })
    }
    // 邀请人
    const inviter = await this.giantService.findByInviteCode(giantInviteDto.inviteCode)
    if (!inviter) {
      throw new InternalServerErrorException({
        statusCode: ENUM_INVITE_STATUS_CODE_ERROR.INVITE_GIANT_NOEXT_ERROR,
        message: 'http.serverError.internalServerError',
        error: '邀请人用户信息错误',
      })
    }
    // 机器码判断邀请自己
    if (intersection(giant.machines.map(m => m.id), inviter.machines.map(m => m.id)).length) {
      throw new InternalServerErrorException({
        statusCode: ENUM_INVITE_STATUS_CODE_ERROR.INVITE_SELF_ERROR,
        message: 'http.serverError.internalServerError',
        error: '不可填写自己的邀请码',
      })
    }
    // ip限制
    const hasMoreIps = await this.inviteService.hasMoreIp(ip)
    if (hasMoreIps) {
      throw new InternalServerErrorException({
        statusCode: ENUM_INVITE_STATUS_CODE_ERROR.INVITE_MORE_IPS_ERROR,
        message: 'http.serverError.internalServerError',
        error: '限制注册(2501), 您无法通过邀请获取秘钥',
      })
    }

    // 机器码限制
    const hasGiant = await this.giantService.findOne({
      machines: {
        $elemMatch: {
          id: machineId
        }
      },
      key: {
        $ne: giant.key
      }
    })
    if (hasGiant) {
      console.log(hasGiant.toObject())
      throw new InternalServerErrorException({
        statusCode: ENUM_INVITE_STATUS_CODE_ERROR.INVITE_MORE_MACHINE_ERROR,
        message: 'http.serverError.internalServerError',
        error: '限制注册(2502), 您无法通过邀请获取秘钥',
      })
    }

    // 1.更新被邀请者
    giant.inviteeCode = inviter.inviteCode
    this.giantService.addDaysToGiant(giant, 30)
    await giant.save()

    // 2.更新邀请者
    ++inviter.inviteCount
    if (inviter.inviteCount >= 8 && inviter.use365) {
      inviter.days = -1
    } else if (inviter.inviteCount >= 16/*  && inviter.use30 */) {
      inviter.days = -1
    } else {
      this.giantService.addDaysToGiant(inviter, 30)
    }
    await inviter.save()

    // 3.新增邀请纪录
    await this.inviteService.create({
      invitee: giant._id,
      inviter: inviter._id,
      ip
    })

    return giant.toObject()
  }

  @AuthGiant()
  @Post('/encryptKrk')
  async encryptKrk(
    // 被邀请人
    @GetGiant() giant: GiantDocument,
    @GetMachineId() machineId: string,
    @Body('data') plain: string,
    @RequestIp() ip: string,
    @Request() req
  ) {
    return await this.giantService.encryptKrk(giant, plain)
  }

  @AuthGiant()
  @Post('/decryptKrk')
  async decryptKrk(
    // 被邀请人
    @GetGiant() giant: GiantDocument,
    @GetMachineId() machineId: string,
    @Body('data') cipher: string,
    @RequestIp() ip: string,
    @Request() req
  ) {
    return await this.giantService.decryptKrk(giant, cipher)
  }

  @Post('/decryptKrkPublic')
  async decryptKrkPublic(
    // 被邀请人
    @GetGiant() giant: GiantDocument,
    @GetMachineId() machineId: string,
    @Body('data') cipher: string,
    @Body('gid') gid: string,
    @RequestIp() ip: string,
    @Request() req
  ) {
    return await this.giantService.decryptKrkPublic(gid, cipher)
  }

  @Response('giant.signKrk')
  @AuthGiant()
  @Post('/signKrk')
  async signKrk(
    // 被邀请人
    @GetGiant() giant: GiantDocument,
    @GetMachineId() machineId: string,
    @Body('data') data: string,
    @RequestIp() ip: string,
    @Request() req
  ) {
    return await this.giantService.signKrk(giant, data)
  }

  @Response('giant.verifyKrk')
  @AuthGiant()
  @Post('/verifyKrk')
  async verifyKrk(
    // 被邀请人
    @GetGiant() giant: GiantDocument,
    @GetMachineId() machineId: string,
    @Body('data') data: string,
    @Body('sign') sign: string,
    @RequestIp() ip: string,
    @Request() req
  ) {
    const isValid = await this.giantService.verifyKrk(giant, data, sign)
    return {
      isValid
    }
  }

  @Response('giant.verify')
  @Post('/verify')
  async verify(
    @Body('key') key: string,
  ) {
    return await this.giantService.verifyDeveloper(key)
  }


  @Response('giant.verifyForce')
  @OptionalGiant()
  @Get('/vf')
  async verifyForce(
    @GetGiant() giant: GiantDocument,
    @GetMachineId() machineId: string,
    @RequestIp() ip: string
  ) {
    try {
      const res = await this.giantService.verifyForce(machineId, giant)
      return {
        pass: res
      };
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: 888,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

} 