import { BadRequestException, Body, Controller, Delete, Get, InternalServerErrorException, Param, Post, Put, Query } from "@nestjs/common";
import { AuthAdminJwtGuard, AuthJwtGuard } from "src/common/auth/decorators/auth.jwt.decorator";
import { ENUM_ERROR_STATUS_CODE_ERROR } from "src/common/error/constants/error.status-code.constant";
import { PaginationService } from "src/common/pagination/services/pagination.service";
import { RequestTimezone } from "src/common/request/decorators/request.decorator";
import { Response, ResponsePaging } from "src/common/response/decorators/response.decorator";
import { IResponsePaging } from "src/common/response/response.interface";
import { ENUM_GIANT_STATUS_CODE_ERROR } from "../constants/giant.status-code.constant";
import { GiantCreateDto } from "../dtos/giant.create.dto";
import { GiantListDto } from "../dtos/giant.list.dto";
import { GiantUpdateDto } from "../dtos/giant.update.dto";
import { GiantListSerializtion } from "../serializations/giant.list.serialization";
import { GiantService } from "../services/giant.service";

@AuthAdminJwtGuard()
@Controller('/giant')
export class GiantController {
  constructor(
    private readonly giantService: GiantService,
    private readonly paginationService: PaginationService,
  ) { }


  @ResponsePaging('giant.list', {
    classSerialization: GiantListSerializtion
  })
  @Get('/list')
  async list(
    @Query()
    {
      current,
      pageSize,
      fields,
      sort,
      search,
      availableSort,
      availableSearch,
    }: GiantListDto
  ): Promise<IResponsePaging> {
    const skip = await this.paginationService.skip(current, pageSize)
    const find: Record<string, any> = Object.assign({
      ...fields
    })

    const giants = await this.giantService.findAll(find, {
      limit: pageSize,
      skip,
      sort
    })
    const total = await this.giantService.getTotal(find)
    const totalPage = await this.paginationService.totalPage(total, pageSize)
    return {
      total,
      totalPage,
      current,
      pageSize,
      availableSearch,
      availableSort,
      data: giants
    }
  }

  @Response('giant.create')
  @Post('/create')
  async create(@Body() giantCreateDto: GiantCreateDto) {
    const exists = await this.giantService.exists(giantCreateDto.key);
    if (exists) {
      throw new BadRequestException({
        statusCode: ENUM_GIANT_STATUS_CODE_ERROR.GIANT_EXIST_ERROR,
        message: 'giant.error.exist',
      });
    }

    try {
      const create = await this.giantService.create(giantCreateDto);
      return create;
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('giant.update')
  @Put('/update/:id')
  async update(
    @Param('id') id: string,
    @Body() giantUpdateDto: GiantUpdateDto,
    @RequestTimezone() timezone: string
  ) {
    try {
      await this.giantService.updateOneById(id, giantUpdateDto, timezone);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('giant.freeze')
  @Put('/freeze/:id/:status')
  async freeze(
    @Param('id') id: string,
    @Param('status') status: string
  ) {
    try {
      return await this.giantService.freeze(id, status == '1');
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('giant.reset')
  @Put('/reset/:id')
  async reset(
    @Param('id') id: string
  ) {
    try {
      return await this.giantService.reset(id);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('giant.delete')
  @Delete('/delete/:id')
  async delete(
    @Param('id') id: string
  ) {
    try {
      return await this.giantService.deleteById(id);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }

  @Response('giant.mail.unbind')
  @Put('/mail/unbind/:id')
  async mailUnbind(
    @Param('id') id: string
  ) {
    try {
      return await this.giantService.unbindMail(id);
    } catch (err: any) {
      throw new InternalServerErrorException({
        statusCode: ENUM_ERROR_STATUS_CODE_ERROR.ERROR_UNKNOWN,
        message: 'http.serverError.internalServerError',
        error: err.message,
      });
    }
  }
}