import { applyDecorators, createParamDecorator, ExecutionContext, UseGuards } from '@nestjs/common';
import { GiantGuard, GiantMachineExceedGuard, GiantMachineExceedWeakGuard, GiantOptionalGuard, GiantWeakGuard } from 'src/common/auth/guards/giant/auth.giant.guard';
import { IGiantDocument } from '../giant.interface';

export const GetGiant = createParamDecorator(
    (data: string, ctx: ExecutionContext): IGiantDocument => {
        const { __giant } = ctx.switchToHttp().getRequest();
        return data ? __giant[data] : __giant;
    }
);

export const GetKey = createParamDecorator(
    (data: string, ctx: ExecutionContext): IGiantDocument => {
        const { __key } = ctx.switchToHttp().getRequest();
        return __key
    }
);

export const GetMachineId = createParamDecorator(
    (data: string, ctx: ExecutionContext): IGiantDocument => {
        const { __machineId } = ctx.switchToHttp().getRequest();
        return __machineId
    }
);

export const GetGiantError = createParamDecorator(
    (data: string, ctx: ExecutionContext): IGiantDocument => {
        const { __giantError } = ctx.switchToHttp().getRequest();
        return __giantError
    }
);

export function AuthGiant(): any {
    return applyDecorators(
        UseGuards(GiantGuard, GiantMachineExceedGuard)
    );
}

export function AuthGiantWeak(): any {
    return applyDecorators(
        UseGuards(GiantWeakGuard, GiantMachineExceedWeakGuard)
    );
}

export function OptionalGiant(): any {
    return applyDecorators(
        UseGuards(GiantOptionalGuard)
    );
}
