import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { AuthModule } from 'src/common/auth/auth.module';
import { AwsModule } from 'src/common/aws/aws.module';
import { SettingController } from 'src/common/setting/controllers/setting.controller';
import { PayModule } from 'src/gateways/gateway.module';
import { HealthController } from 'src/health/controllers/health.controller';
import { HealthModule } from 'src/health/health.module';
import { JobsModule } from 'src/jobs/jobs.module';
import { CAModule } from 'src/modules/ca/ca.module';
import { CAController } from 'src/modules/ca/controllers/ca.controller';
import { DownloadController } from 'src/modules/download/controllers/download.controller';
import { DownloadModule } from 'src/modules/download/download.module';
import { ExtensionController } from 'src/modules/extension/controllers/extension.controller';
import { ExtensionModule } from 'src/modules/extension/extension.module';
import { ExtraController } from 'src/modules/extra/controllers/extra.controller';
import { ExtraModule } from 'src/modules/extra/extra.module';
import { FeedbackController } from 'src/modules/feedback/controllers/feedback.controller';
import { FeedbackModule } from 'src/modules/feedback/feedback.module';
import { GiantController } from 'src/modules/giant/controllers/giant.user.controller';
import { GiantModule } from 'src/modules/giant/giant.module';
import { OrderController } from 'src/modules/order/controllers/order.controller';
import { OrderModule } from 'src/modules/order/order.module';
import { PaycodeController } from 'src/modules/paycode/controllers/paycode.controller';
import { PaycodeModule } from 'src/modules/paycode/paycode.module';
import { PayconfigModule } from 'src/modules/payconfig/payconfig.module';
import { PermissionModule } from 'src/modules/permission/permission.module';
import { RoleModule } from 'src/modules/role/role.module';
import { UserController } from 'src/modules/user/controllers/user.controller';
import { UserModule } from 'src/modules/user/user.module';
import { VersionController } from 'src/modules/version/controllers/version.controller';
import { VersionModule } from 'src/modules/version/version.module';
import { TestController } from 'src/test/controllers/test.controller';
import { TestModule } from 'src/test/test.module';
import { AuthController } from '../../common/auth/controllers/auth.controller';
import { RecordModule } from 'src/modules/record/record.module';
import { RecordController } from 'src/modules/record/controllers/record.user.controller';

@Module({
    controllers: [
        SettingController,
        UserController,
        HealthController,
        AuthController,

        ExtraController,
        OrderController,
        GiantController,
        FeedbackController,
        ExtensionController,
        DownloadController,
        PaycodeController,
        TestController,
        VersionController,
        RecordController
    ],
    providers: [],
    exports: [],
    imports: [
        UserModule,
        AuthModule,
        AwsModule,
        PermissionModule,
        RoleModule,
        HealthModule,
        TerminusModule,
        HttpModule,
        JobsModule,
        PayModule,
        TestModule,

        ExtraModule,
        OrderModule,
        GiantModule,
        FeedbackModule,
        ExtensionModule,
        PayconfigModule,
        PaycodeModule,
        DownloadModule,
        VersionModule,
        RecordModule,
    ],
})
export class RoutesModule { }
