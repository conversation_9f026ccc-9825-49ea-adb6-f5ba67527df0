import { Module } from '@nestjs/common';
import { AuthModule } from 'src/common/auth/auth.module';
import { SettingAdminController } from 'src/common/setting/controllers/setting.admin.controller';
import { DownloadController } from 'src/modules/download/controllers/download.admin.controller';
import { DownloadModule } from 'src/modules/download/download.module';
import { ExtensionController } from 'src/modules/extension/controllers/extension.admin.controller';
import { ExtensionModule } from 'src/modules/extension/extension.module';
import { FeedbackController } from 'src/modules/feedback/controllers/feedback.admin.controller';
import { FeedbackModule } from 'src/modules/feedback/feedback.module';
import { GiantController } from 'src/modules/giant/controllers/giant.admin.controller';
import { GiantModule } from 'src/modules/giant/giant.module';
import { PayconfigController } from 'src/modules/payconfig/controllers/payconfig.admin.controller';
import { PayconfigModule } from 'src/modules/payconfig/payconfig.module';
import { PermissionAdminController } from 'src/modules/permission/controllers/permission.admin.controller';
import { PermissionModule } from 'src/modules/permission/permission.module';
import { ReportController } from 'src/modules/report/controllers/report.admin.controller';
import { ReportModule } from 'src/modules/report/report.module';
import { RoleAdminController } from 'src/modules/role/controllers/role.admin.controller';
import { RoleModule } from 'src/modules/role/role.module';
import { StatisticsController } from 'src/modules/statistics/controllers/statistics.controller';
import { StatisticsModule } from 'src/modules/statistics/statistics.module';
import { UserAdminController } from 'src/modules/user/controllers/user.admin.controller';
import { UserModule } from 'src/modules/user/user.module';

@Module({
    controllers: [
        SettingAdminController,
        UserAdminController,
        RoleAdminController,
        PermissionAdminController,

        GiantController,
        ExtensionController,
        PayconfigController,
        FeedbackController,
        DownloadController,
        StatisticsController,
        ReportController,
    ],
    providers: [],
    exports: [],
    imports: [
        UserModule,
        AuthModule,
        RoleModule,
        PermissionModule,

        GiantModule,
        ExtensionModule,
        PayconfigModule,
        FeedbackModule,
        ReportModule,
        DownloadModule,
        StatisticsModule,
    ],
})
export class RoutesAdminModule { }
