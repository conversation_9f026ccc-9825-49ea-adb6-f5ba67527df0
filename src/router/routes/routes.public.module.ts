import { Module } from '@nestjs/common';
import { AuthModule } from 'src/common/auth/auth.module';
import { CAModule } from 'src/modules/ca/ca.module';
import { CAController } from 'src/modules/ca/controllers/ca.controller';
import { DownloadPublicController } from 'src/modules/download/controllers/download.public.controller';
import { DownloadModule } from 'src/modules/download/download.module';
import { ExtensionPublicController } from 'src/modules/extension/controllers/extension.public.controller';
import { ExtensionModule } from 'src/modules/extension/extension.module';
import { GiantModule } from 'src/modules/giant/giant.module';
import { PayconfigPublicController } from 'src/modules/payconfig/controllers/payconfig.public.controller';
import { PayconfigModule } from 'src/modules/payconfig/payconfig.module';
import { PermissionModule } from 'src/modules/permission/permission.module';
import { ReportController } from 'src/modules/report/controllers/report.controller';
import { ReportModule } from 'src/modules/report/report.module';
import { RoleModule } from 'src/modules/role/role.module';
import { UserPublicController } from 'src/modules/user/controllers/user.public.controller';
import { UserModule } from 'src/modules/user/user.module';

@Module({
  controllers: [
    UserPublicController,

    ExtensionPublicController,
    PayconfigPublicController,
    DownloadPublicController,
    ReportController,
    CAController,
  ],
  providers: [],
  exports: [],
  imports: [
    UserModule,
    AuthModule,
    RoleModule,
    PermissionModule,

    ExtensionModule,
    PayconfigModule,
    DownloadModule,
    GiantModule,
    ReportModule,
    CAModule,

  ],
})
export class RoutesPublicModule { }
