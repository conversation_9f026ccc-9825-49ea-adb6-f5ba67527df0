import { Module } from '@nestjs/common';
import { BoardModule } from 'src/modules/community/board/board.module';
import { BoardController } from 'src/modules/community/board/controllers/board.admin.controller';
import { BoardGroupModule } from 'src/modules/community/boardGroup/boardGroup.module';
import { BoardGroupController } from 'src/modules/community/boardGroup/controllers/boardGroup.admin.controller';
import { DictController } from 'src/modules/community/dict/controllers/dict.admin.controller';
import { DictModule } from 'src/modules/community/dict/dict.module';
import { PermissionController } from "src/modules/community/permission/controllers/permission.admin.controller";
import { PermissionModule } from "src/modules/community/permission/permission.module";
import { RoleController } from 'src/modules/community/role/controllers/role.admin.controller';
import { RoleModule } from 'src/modules/community/role/role.module';
import { SyncController } from 'src/modules/community/sync/controllers/sync.controller';
import { SyncModule } from 'src/modules/community/sync/sync.module';
import { TopicController } from 'src/modules/community/topic/controllers/topic.admin.controller';
import { TopicModule } from 'src/modules/community/topic/topic.module';
import { UserController } from 'src/modules/community/user/controllers/user.admin.controller';
import { UserModule } from 'src/modules/community/user/user.module';

@Module({
    controllers: [
        PermissionController,
        RoleController,
        UserController,
        DictController,
        SyncController,
        TopicController,
        BoardController,
        BoardGroupController,
    ],
    providers: [],
    exports: [],
    imports: [
        PermissionModule,
        RoleModule,
        UserModule,
        DictModule,
        SyncModule,
        TopicModule,
        BoardModule,
        BoardGroupModule,
    ],
})
export class RoutesCommunityModule { }
