name: Test
on:
  pull_request:
    branches: 
    - main
    
jobs:
  test:
    runs-on: ubuntu-latest
    environment: baibay

    strategy:
      matrix:
        node-version: ['17.x']

    services:
      mongodb:
        image: mongo:latest
        options: >-
          --health-cmd mongo
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 27017:27017

    steps:
      - name: Git checkout
        uses: actions/checkout@v3

      - name: Setup node version ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install dependencies
        run: yarn --frozen-lockfile

      - name: Create env file
        run: |
          touch .env
          echo APP_NAME="ack" >> .env
          echo APP_ENV="development" >> .env
          echo APP_MODE="secure" >> .env
          echo APP_LANGUAGE="en" >> .env
          echo APP_TZ="Asia/Jakarta" >> .env
          echo APP_HOST="localhost" >> .env
          echo APP_PORT=" 3000" >> .env
          echo APP_DEBUG="false" >> .env
          echo APP_VERSIONING="true" >> .env
          echo APP_VERSION="1" >> .env
          echo APP_HTTP_ON="true" >> .env
          echo APP_JOB_ON="true" >> .env
          echo DATABASE_HOST="mongodb://localhost:27017" >> .env
          echo DATABASE_NAME="ack" >> .env
          echo DATABASE_USER="" >> .env
          echo DATABASE_PASSWORD="" >> .env
          echo DATABASE_DEBUG="false" >> .env
          echo DATABASE_OPTIONS="" >> .env
          echo MIDDLEWARE_TOLERANCE_TIMESTAMP="5m" >> .env
          echo MIDDLEWARE_TIMEOUT="30s" >> .env
          echo AUTH_JWT_AUDIENCE="https://example.com" >> .env
          echo AUTH_JWT_ISSUER="ack" >> .env
          echo AUTH_JWT_ACCESS_TOKEN_SECRET_KEY="123456" >> .env
          echo AUTH_JWT_ACCESS_TOKEN_EXPIRED="30m" >> .env
          echo AUTH_JWT_REFRESH_TOKEN_SECRET_KEY="01001231" >> .env
          echo AUTH_JWT_REFRESH_TOKEN_EXPIRED="7d" >> .env
          echo AUTH_JWT_REFRESH_TOKEN_REMEMBER_ME_EXPIRED="30d" >> .env
          echo AUTH_JWT_REFRESH_TOKEN_NOT_BEFORE_EXPIRATION="0" >> .env
          echo AUTH_BASIC_TOKEN_CLIENT_ID="asdzxc" >> .env
          echo AUTH_BASIC_TOKEN_CLIENT_SECRET="1234567890" >> .env
          echo AWS_CREDENTIAL_KEY="$AWS_CREDENTIAL_KEY" >> .env
          echo AWS_CREDENTIAL_SECRET="$AWS_CREDENTIAL_SECRET" >> .env
          echo AWS_S3_BUCKET="$AWS_S3_BUCKET" >> .env
          echo AWS_S3_REGION="$AWS_S3_REGION" >> .env
        env:
          AWS_CREDENTIAL_KEY: ${{ secrets.AWS_CREDENTIAL_KEY }}
          AWS_CREDENTIAL_SECRET: ${{ secrets.AWS_CREDENTIAL_SECRET }}
          AWS_S3_BUCKET: ${{ secrets.AWS_S3_BUCKET }}
          AWS_S3_REGION: ${{ secrets.AWS_S3_REGION }}
          
      - name: Migration
        run: yarn migrate

      - name: Unit Test
        run: yarn test:unit

      - name: Unit Integration
        run: yarn test:integration

      - name: E2E Test
        run: yarn test:e2e
