name: Greetings
on: [pull_request, issues]

jobs:
  greeting:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
    - uses: actions/first-interaction@v1
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        issue-message: 'Thank you for opening your first issue here! Please be patient until your request is processed 🚀'
        pr-message: 'Thank you for opening this pull request! Please be patient until your changes are reviewed 💌'
