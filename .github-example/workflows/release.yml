name: Release

on:
  workflow_run:
    workflows: ['CI']
    types:
      - completed

jobs:
  release:
    runs-on: ubuntu-latest

    steps:
      - name: Git checkout
        uses: actions/checkout@v3

      - name: Get commit
        id: git
        run: |
          echo "::set-output name=short_sha::$(git rev-parse --short HEAD)"
      
      - name: Get latest version
        id: version
        uses: martinbeentjes/npm-get-version-action@main

      - name: Git
        run: |
          echo Branch name is: ${{ github.ref_name }}
          echo Short sha: ${{ steps.git.outputs.short_sha}}
          echo Version is: ${{ steps.version.outputs.current-version}}

      - name: Release
        uses: softprops/action-gh-release@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ steps.version.outputs.current-version}}
          name: v${{ steps.version.outputs.current-version}}
          generate_release_notes: true
          draft: false
          prerelease: false
