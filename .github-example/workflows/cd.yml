name: CD

on:
  # workflow_run:
  #   workflows: ['CI']
  #   types:
  #     - completed
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    env: 
      APP_NAME: ack-nestjs-mongoose
      APP_PORT: 3000
      APP_NETWORK: app-network

    steps:

      - name: Git checkout
        uses: actions/checkout@v3

      - name: Get commit
        id: git
        run: |
          echo "::set-output name=short_sha::$(git rev-parse --short HEAD)"
      
      - name: Get latest version
        id: version
        uses: martinbeentjes/npm-get-version-action@main

      - name: Git
        run: |
          echo Branch name is: ${{ github.ref_name }}
          echo Short sha: ${{ steps.git.outputs.short_sha}}
          echo Version is: ${{ steps.version.outputs.current-version}}

      - name: Environment
        run: |
          echo APP_NAME is: ${{ env.APP_NAME}}
          echo APP_PORT is: ${{ env.APP_PORT}}
          echo APP_NETWORK is: ${{ env.APP_NETWORK}}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Deploy
        uses: fifsky/ssh-action@master
        with:
          command: |
            docker pull ${{ secrets.DOCKERHUB_USERNAME }}/${{ env.APP_NAME}}:${{ steps.git.outputs.short_sha }}
            docker stop ${{ env.APP_NAME}} && docker rm ${{ env.APP_NAME}}
            docker network create ${{ env.APP_NETWORK }} --driver=bridge
            docker run -itd \
              --hostname ${{ env.APP_NAME}} \
              --publish ${{ env.APP_PORT }}:${{ env.APP_PORT }} \
              --network ${{ env.APP_NETWORK }} \
              --volume /app/${{ env.APP_NAME}}/logs/:/app/logs/ \
              --volume /app/${{ env.APP_NAME}}/.env:/app/.env \
              --restart unless-stopped \
              --name ${{ env.APP_NAME}} ${{ secrets.DOCKERHUB_USERNAME }}/${{ env.APP_NAME}}:v${{ steps.version.outputs.current-version}}
          host: ${{ secrets.SSH_HOST }}
          port: ${{ secrets.SSH_PORT }}
          user: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY}}

      - name: Clean
        uses: fifsky/ssh-action@master
        continue-on-error: true
        with:
          command: |
            docker container prune --force
            docker image prune --force
            docker rmi $(docker images **/${{ secrets.APP_NAME }} -q) --force
          host: ${{ secrets.SSH_HOST }}
          port: ${{ secrets.SSH_PORT }}
          user: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY}}