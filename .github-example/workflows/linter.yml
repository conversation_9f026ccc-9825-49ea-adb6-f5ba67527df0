name: <PERSON><PERSON>
on:
  pull_request:
    branches: 
    - main

jobs:
  linter:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: ['17.x']

    steps:
      - name: Git checkout
        uses: actions/checkout@v3

      - name: Git sort sha
        run: echo ${{ steps.vars.outputs.sha_short }}

      - name: Setup node version ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install dependencies
        run: yarn --frozen-lockfile
          
      - name: Linter
        run: yarn lint
