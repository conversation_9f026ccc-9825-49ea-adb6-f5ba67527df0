pipeline {
    agent any

    stages {
        stage('Build') {
            steps {
                echo 'Building..'
				sh 'docker build -t k-service:latest .'
                echo 'Build Success'
				// sh 'yarn build:service'
				// sh 'yarn build:admin'
				// sh 'yarn build:home'
				// sh 'yarn build:docs'
            }
        }
        stage('Test') {
            steps {
                echo 'Testing..'
            }
        }
        stage('Deploy') {
            steps {
                echo 'Deploying....'
				sh 'chmod +x ./deploy/deploy.sh'
				sh './deploy/deploy.sh'
            }
        }
    }
}