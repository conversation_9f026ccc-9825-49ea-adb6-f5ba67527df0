{"testTimeout": 10000, "rootDir": "../../", "modulePaths": ["."], "testEnvironment": "node", "testMatch": ["<rootDir>/test/integration/**/*.spec.ts"], "collectCoverage": true, "coverageDirectory": "integration-coverage", "collectCoverageFrom": ["./integration"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "moduleFileExtensions": ["js", "ts", "json"], "transform": {"^.+\\.(t|j)s$": "ts-jest"}}