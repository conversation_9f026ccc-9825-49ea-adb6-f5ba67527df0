{"testTimeout": 10000, "rootDir": "../../", "modulePaths": ["."], "testEnvironment": "node", "testMatch": ["<rootDir>/test/unit/**/*.spec.ts"], "collectCoverage": true, "coverageDirectory": "coverage", "collectCoverageFrom": ["./src/common/auth/services/**", "./src/common/database/services/**", "./src/common/debugger/services/**", "./src/common/logger/services/**", "./src/common/message/services/**", "./src/common/pagination/services/**", "./src/common/setting/services/**", "./src/common/helper/services/**"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "moduleFileExtensions": ["js", "ts", "json"], "transform": {"^.+\\.(t|j)s$": "ts-jest"}}