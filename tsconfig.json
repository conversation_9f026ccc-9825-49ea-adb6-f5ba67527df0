{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": false,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "useDefineForClassFields": false,
    "target": "ESNext",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "allowJs": false,
    "incremental": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "moduleResolution": "node",     
    "resolveJsonModule": true,
    "paths": {
      "src/*": ["./src/*"]
    }
  },
  "include": [
    "src",
    "test",
  ],
  "exclude": [
    "node_modules",
    "dist", 
    "docker"
  ]
}
