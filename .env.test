APP_NAME=kraken
APP_ENV=development
APP_MODE=simple
APP_LANGUAGE=en
APP_TZ=Asia/Shanghai

APP_HOST=0.0.0.0
APP_PORT= 4000
APP_DEBUG=false

APP_VERSIONING=true
APP_VERSION=1

APP_HTTP_ON=true
APP_JOB_ON=true

DATABASE_HOST=mongodb://*************:27016
DATABASE_NAME=krakentest
DATABASE_USER=reignofwebber
DATABASE_PASSWORD=6Qbbr3qq
DATABASE_DEBUG=false
DATABASE_OPTIONS=

COMMUNITY_DATABASE_HOST=mongodb://*************:27016
COMMUNITY_DATABASE_NAME=k-community
COMMUNITY_DATABASE_USER=reignofwebber
COMMUNITY_DATABASE_PASSWORD=6Qbbr3qq
COMMUNITY_DATABASE_DEBUG=false
COMMUNITY_DATABASE_OPTIONS=

MIDD<PERSON>WARE_TOLERANCE_TIMESTAMP=5m
MIDD<PERSON>WARE_TIMEOUT=30s

AUTH_JWT_AUDIENCE=https://example.com
AUTH_JWT_ISSUER=ack
AUTH_JWT_ACCESS_TOKEN_SECRET_KEY=123456
AUTH_JWT_ACCESS_TOKEN_EXPIRED=2m
AUTH_JWT_REFRESH_TOKEN_SECRET_KEY=01001231
AUTH_JWT_REFRESH_TOKEN_EXPIRED=7d
AUTH_JWT_REFRESH_TOKEN_REMEMBER_ME_EXPIRED=30d
AUTH_JWT_REFRESH_TOKEN_NOT_BEFORE_EXPIRATION=1m

AUTH_BASIC_TOKEN_CLIENT_ID=asdzxc
AUTH_BASIC_TOKEN_CLIENT_SECRET=1234567890

STATIC_BASE_PATH=dist/release
DOWNLOAD_BASE_PATH=kraken/download

BACKUP_PATH=D:\backup

AWS_CREDENTIAL_KEY=
AWS_CREDENTIAL_SECRET=
AWS_S3_REGION=us-east-2
AWS_S3_BUCKET=acks3