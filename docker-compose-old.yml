version: '3.8'
services:
  service:
    build: .
    container_name: ack
    hostname: ack
    ports:
      - 3000:3000
    networks:
      - app-network
    volumes:
      - ./src/:/app/src/
      - .env/:/app/.env
    restart: unless-stopped
    depends_on: 
      - mongodb
  mongodb:
    image: mongo:latest
    container_name: mongo
    hostname: mongo
    ports:
      - 27017:27017
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: 123456
      MONGO_INITDB_DATABASE: ack
    volumes:
      - dbdata:/data/db
    restart: unless-stopped
    networks:
      - app-network
networks:
  app-network:
    name: app-network
    driver: bridge
volumes:
  dbdata: