APP_NAME=ack
APP_ENV=development
APP_MODE=simple
APP_LANGUAGE=en
APP_TZ=Asia/Jakarta

APP_HOST=localhost
APP_PORT= 3000
APP_DEBUG=false

APP_VERSIONING=true
APP_VERSION=1

APP_HTTP_ON=true
APP_JOB_ON=true

DATABASE_HOST=mongodb://mongodb:27017
DATABASE_NAME=ack
DATABASE_USER=root
DATABASE_PASSWORD=123456
DATABASE_DEBUG=false
DATABASE_OPTIONS=authSource=admin

MIDD<PERSON>WARE_TOLERANCE_TIMESTAMP=5m
MIDDLEWARE_TIMEOUT=30s

AUTH_JWT_AUDIENCE=https://example.com
AUTH_JWT_ISSUER=ack
AUTH_JWT_ACCESS_TOKEN_SECRET_KEY=123456
AUTH_JWT_ACCESS_TOKEN_EXPIRED=30m
AUTH_JWT_REFRESH_TOKEN_SECRET_KEY=01001231
AUTH_JWT_REFRESH_TOKEN_EXPIRED=7d
AUTH_JWT_REFRESH_TOKEN_REMEMBER_ME_EXPIRED=30d
AUTH_JWT_REFRESH_TOKEN_NOT_BEFORE_EXPIRATION=30m

AUTH_BASIC_TOKEN_CLIENT_ID=asdzxc
AUTH_BASIC_TOKEN_CLIENT_SECRET=1234567890

AWS_CREDENTIAL_KEY=
AWS_CREDENTIAL_SECRET=
AWS_S3_REGION=us-east-2
AWS_S3_BUCKET=acks3
