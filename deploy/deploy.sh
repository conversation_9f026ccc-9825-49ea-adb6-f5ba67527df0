#!/bin/sh
# sudo mkdir -p /usr/share/nginx/html/test/admin
# sudo mkdir -p /usr/share/nginx/html/test/home
# sudo mkdir -p /usr/share/nginx/html/test/fallback
# sudo mkdir -p /usr/share/nginx/html/test/static/kraken/docs/
# sudo mv -n deploy/index.html /usr/share/nginx/html/test/fallback/

# sudo rsync -av --delete packages/@admin/dist/ /usr/share/nginx/html/test/admin/
# sudo rsync -av --delete packages/@home/dist/ /usr/share/nginx/html/test/home/
# sudo rsync -av --delete packages/@docs/dist/ /usr/share/nginx/html/test/static/kraken/docs/

sudo mkdir -p /root/app/kraken/service
sudo cp deploy/docker-compose.yml /root/app/kraken/service/
sudo cp .env.prod /root/app/kraken/service/.env
sudo docker compose -f /root/app/kraken/service/docker-compose.yml down
sudo docker compose -f /root/app/kraken/service/docker-compose.yml up -d
