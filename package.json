{"name": "@kraken/service", "version": "2.2.2", "description": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/andrechristikan/ack-nestjs-mongoose.git"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "private": true, "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "cross-env NODE_ENV=production nest build", "build-watch": "nest build --watch", "format": "prettier --write '{src,test}/**/*.ts'", "format:src": "prettier --write src/**/*.ts", "format:test": "prettier --write test/**/*.ts", "rollback": "cross-env NODE_ENV=development nestjs-command remove:user && nestjs-command remove:role && nestjs-command remove:permission && nestjs-command remove:authapis && nestjs-command remove:setting", "migrate": "cross-env NODE_ENV=development nestjs-command insert:setting && cross-env NODE_ENV=development nestjs-command insert:authapis && cross-env NODE_ENV=development nestjs-command insert:permission && cross-env NODE_ENV=development nestjs-command insert:role && cross-env NODE_ENV=development nestjs-command insert:user", "start": "node dist/src/main", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:debug": "cross-env NODE_ENV=development nest start --debug --watch", "start:prod": "cross-env NODE_ENV=production node dist/src/main", "lint": "eslint --ext .ts,.tsx '{src,test}/**/*.ts' --no-error-on-unmatched-pattern", "lint:fix": "eslint --ext .ts,.tsx '{src,test}/**/*.ts' --fix --no-error-on-unmatched-pattern", "lint:src": "eslint --ext .ts,.tsx src/**/*.ts --no-error-on-unmatched-pattern", "lint:test": "eslint --ext .ts,.tsx test/**/*.ts --no-error-on-unmatched-pattern", "test": "jest --config test/unit/jest.json --passWithNoTests --forceExit && jest --config test/integration/jest.json --passWithNoTests --forceExit && jest --config test/e2e/jest.json --passWithNoTests --forceExit", "test:unit": "jest --config test/unit/jest.json --passWithNoTests --forceExit", "test:integration": "jest --config test/integration/jest.json --passWithNoTests --forceExit", "test:e2e": "jest --config test/e2e/jest.json --passWithNoTests --forceExit", "deadcode": "ts-prune --project tsconfig.json", "deadcode:filter": "ts-prune --project tsconfig.json | grep -v '(used in module)'", "deadcode:count": "ts-prune --project tsconfig.json | grep -v '(used in module)' | wc -l", "spell": "cspell lint --config cspell.json {src,test}/**/*.ts prod/* readme.md tsconfig*.json docker* package.json nodemon.json license.md .github/* .husky/* --color --gitignore --no-must-find-files --no-summary --no-progress || true", "spell:src": "cspell lint --config cspell.json src/**/*.ts --color --gitignore --no-must-find-files --no-summary --no-progress || true", "spell:test": "cspell lint --config cspell.json test/**/*.ts --color --gitignore --no-must-find-files --no-summary --no-progress || true"}, "dependencies": {"@aws-sdk/client-s3": "^3.154.0", "@faker-js/faker": "^7.4.0", "@joi/date": "^2.1.0", "@nestjs/axios": "^0.1.0", "@nestjs/common": "9.0.11", "@nestjs/config": "^2.2.0", "@nestjs/core": "9.0.11", "@nestjs/jwt": "^9.0.0", "@nestjs/mapped-types": "^1.1.0", "@nestjs/mongoose": "^9.2.0", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "9.0.11", "@nestjs/platform-socket.io": "9.0.11", "@nestjs/platform-ws": "9.0.11", "@nestjs/schedule": "^2.1.0", "@nestjs/terminus": "^9.1.1", "@nestjs/websockets": "9.0.11", "@types/adm-zip": "^0.5.0", "@types/markdown-it": "^12.2.3", "@ultraknown/cos-nodejs-sdk-v5": "^2.12.4", "add": "^2.0.6", "adm-zip": "^0.5.9", "bcrypt": "^5.0.1", "body-parser": "^1.20.2", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "clipboardy": "^3.0.0", "compression": "^1.7.4", "crypto-js": "^4.1.1", "date-fns": "^2.29.3", "express-rate-limit": "^6.5.1", "fs-extra": "^10.1.0", "geolib": "^3.3.3", "helmet": "^5.1.1", "jimp": "^0.16.1", "joi": "^17.6.0", "jsdom": "^24.0.0", "jszip": "^3.10.1", "markdown-it": "^13.0.1", "marked": "^12.0.1", "moment": "^2.29.4", "moment-timezone": "^0.5.34", "mongodb": "^6.1.0", "mongoose": "^6.5.2", "morgan": "^1.10.0", "nest-winston": "^1.7.0", "nestjs-command": "^3.1.2", "nestjs-i18n": "^9.1.6", "nodemailer": "^6.8.0", "passport": "^0.6.0", "passport-headerapikey": "^1.2.2", "passport-jwt": "^4.0.0", "qrcode-reader": "^1.0.4", "reflect-metadata": "^0.1.13", "request-ip": "^3.3.0", "response-time": "^2.3.2", "rimraf": "^3.0.2", "rotating-file-stream": "^3.0.4", "rxjs": "^7.5.6", "ua-parser-js": "^1.0.2", "uuid": "^8.3.2", "winston": "^3.8.1", "winston-daily-rotate-file": "^4.7.1", "xlsx": "^0.18.5", "yargs": "^17.5.1"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.1", "@nestjs/testing": "9.0.11", "@types/bcrypt": "^5.0.0", "@types/body-parser": "^1.19.3", "@types/bytes": "^3.1.1", "@types/compression": "^1.7.2", "@types/cors": "^2.8.12", "@types/crypto-js": "^4.1.1", "@types/express": "^4.17.13", "@types/fs-extra": "^9.0.13", "@types/jest": "^28.1.7", "@types/jsdom": "^21.1.6", "@types/lodash": "^4.14.184", "@types/morgan": "^1.9.3", "@types/ms": "^0.7.31", "@types/multer": "^1.4.7", "@types/node": "^18.7.8", "@types/nodemailer": "^6.4.6", "@types/passport-jwt": "^3.0.6", "@types/request-ip": "^0.0.37", "@types/response-time": "^2.3.5", "@types/supertest": "^2.0.12", "@types/ua-parser-js": "^0.7.36", "@types/uuid": "^8.3.4", "@types/ws": "^8.5.3", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "cross-env": "^7.0.3", "cspell": "^6.8.0", "eslint": "^8.22.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "husky": "^8.0.1", "jest": "^28.1.3", "prettier": "^2.7.1", "supertest": "^6.2.4", "ts-jest": "^28.0.8", "ts-loader": "^9.3.1", "ts-node": "^10.9.1", "ts-prune": "^0.10.3", "tsconfig-paths": "^4.1.0", "typescript": "^4.7.4"}}