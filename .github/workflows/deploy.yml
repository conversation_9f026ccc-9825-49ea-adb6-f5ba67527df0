name: Deploy
run-name: ${{ github.actor }} is deploying GitHub Actions 🚀
on:
  push:
    branches:
      - main
jobs:
  Deploy-to-Server:
    runs-on: [self-hosted, Linux]
    steps:
      - name: Check out repository code
        uses: actions/checkout@v4
      - name: Deploying...
        run: |
          docker build -t k-service:latest ${{ github.workspace }}
          chmod +x ${{ github.workspace }}/deploy/deploy.sh
          ${{ github.workspace }}/deploy/deploy.sh
      - run: echo "🍏 This job's status is ${{ job.status }}."
