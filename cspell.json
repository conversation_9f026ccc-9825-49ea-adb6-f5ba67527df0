{"version": "0.2", "language": "en", "words": ["<PERSON><PERSON><PERSON>", "metatype", "virtuals", "prebuild", "logform", "transfomer", "insync", "microservices", "globby", "docker<PERSON>b", "<PERSON><PERSON><PERSON>", "buildx", "exceljs", "milis", "workdir", "dbdata", "initdb", "deadcode", "autha<PERSON>", "headerapikey", "jen<PERSON><PERSON><PERSON>", "superadmin", "alphanum", "dtos"], "ignoreWords": ["psheon", "aallithioo", "tia<PERSON>o", "qwertyuiop12345zxcvbnmkjh", "opbUwdiS1FBsrDUoPgZdx", "cuwakimacojulawu", "baibay", "acks", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "ignorePaths": ["node_modules/**", "endpoints/**", "*coverage/**", ".husky/**", ".github/**", "dist/**", "logs/**", "src/database/json/**"]}