APP_NAME=kraken
APP_ENV=production
APP_MODE=simple
APP_LANGUAGE=en
APP_TZ=Asia/Shanghai

APP_HOST=0.0.0.0
APP_PORT= 3000
APP_DEBUG=false

APP_VERSIONING=true
APP_VERSION=1

APP_HTTP_ON=true
APP_JOB_ON=true

DATABASE_HOST=mongodb://mongo:27017
DATABASE_NAME=kraken
DATABASE_USER=reignofwebber
DATABASE_PASSWORD=6Qbbr3qq
DATABASE_DEBUG=false
DATABASE_OPTIONS=

COMMUNITY_DATABASE_HOST=mongodb://mongo:27017
COMMUNITY_DATABASE_NAME=k-community
COMMUNITY_DATABASE_USER=reignofwebber
COMMUNITY_DATABASE_PASSWORD=6Qbbr3qq
COMMUNITY_DATABASE_DEBUG=false
COMMUNITY_DATABASE_OPTIONS=

MIDDLEWARE_TOLERANCE_TIMESTAMP=5m
MIDDLEWARE_TIMEOUT=30s

AUTH_JWT_AUDIENCE=https://example.com
AUTH_JWT_ISSUER=ack
AUTH_JWT_ACCESS_TOKEN_SECRET_KEY=123456
AUTH_JWT_ACCESS_TOKEN_EXPIRED=30m
AUTH_JWT_REFRESH_TOKEN_SECRET_KEY=01001231
AUTH_JWT_REFRESH_TOKEN_EXPIRED=7d
AUTH_JWT_REFRESH_TOKEN_REMEMBER_ME_EXPIRED=30d
AUTH_JWT_REFRESH_TOKEN_NOT_BEFORE_EXPIRATION=30m

AUTH_BASIC_TOKEN_CLIENT_ID=asdzxc
AUTH_BASIC_TOKEN_CLIENT_SECRET=1234567890

STATIC_PREFIX=/release
STATIC_BASE_PATH=/app/release
DOWNLOAD_BASE_PATH=/kraken
DOWNLOAD_DOMAIN=http://static.ultraknown.com/

BACKUP_PATH=/usr/share/kraken/prod

AWS_CREDENTIAL_KEY=
AWS_CREDENTIAL_SECRET=
AWS_S3_REGION=us-east-2
AWS_S3_BUCKET=acks3

# mail
MAIL_HOST=smtp.qcloudmail.com
MAIL_PORT=465
MAIL_USER=<EMAIL>
MAIL_PASS="8zD#s2bFq9oL\$yW1pA3"
MAIL_FROM=<EMAIL>
MAIL_SENDER=ultraknown

# cos
COS_SECRET_ID=AKID0xsdAWfZIn6DB2f3LWVGrJcqrgOwiLkz
COS_SECRET_KEY=A9uWzIUaV3TltDLK9k9NTM3eKgy8rWHn
COS_BUCKET=kraken-community-1300097400
COS_REGION=ap-nanjing
